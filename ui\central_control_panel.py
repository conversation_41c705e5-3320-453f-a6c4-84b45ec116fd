# -*- coding: utf-8 -*-
"""
لوحة التحكم المركزية الجذابة لبرنامج المحاسبة العربي
تصميم حديث بألوان دافئة وجذابة مع دعم كامل للـ RTL
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, filedialog, colorchooser
import json
import os
from pathlib import Path
from datetime import datetime
import sqlite3
from typing import Dict, Any, Optional

# استيراد الثيمات والإعدادات
from themes.modern_theme import MODERN_COLORS, FONTS, DIMENSIONS
from config.settings import PROJECT_ROOT, DATABASE_PATH

# ألوان دافئة وجذابة جديدة (بدون رمادي)
WARM_COLORS = {
    # ألوان دافئة أساسية
    'coral': '#FF6B6B',           # مرجاني دافئ
    'sunset': '#FF8E53',          # برتقالي غروب
    'golden': '#FFD93D',          # ذهبي مشرق
    'mint': '#6BCF7F',            # نعناعي منعش
    'lavender': '#A8E6CF',        # لافندر هادئ
    'sky': '#4ECDC4',             # سماوي صافي
    'rose': '#FF8A95',            # وردي ناعم
    'peach': '#FFAAA5',           # خوخي فاتح
    'turquoise': '#45B7D1',       # تركوازي
    'violet': '#96CEB4',          # بنفسجي فاتح
    'cream': '#FFF8E1',           # كريمي دافئ
    'ivory': '#FFFEF7',           # عاجي
    
    # ألوان خلفية دافئة
    'warm_white': '#FFFEF7',      # أبيض دافئ
    'soft_cream': '#FFF8E1',      # كريمي ناعم
    'light_peach': '#FFF5F5',     # خوخي فاتح جداً
    'mint_bg': '#F0FFF4',         # خلفية نعناعية
    
    # ألوان نص دافئة
    'dark_coral': '#E55555',      # مرجاني داكن
    'warm_brown': '#8B4513',      # بني دافئ
    'deep_teal': '#2C5F5D',       # تيل عميق
    'rich_purple': '#6A4C93',     # بنفسجي غني
}

class CentralControlPanel:
    """لوحة التحكم المركزية الجذابة"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.settings_file = PROJECT_ROOT / "config" / "control_panel_settings.json"
        self.current_settings = {}
        self.sidebar_buttons = {}
        self.current_section = "general"
        
        # إنشاء النافذة الرئيسية
        self.create_main_window()
        
        # تحميل الإعدادات المحفوظة
        self.load_settings()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # عرض القسم الافتراضي
        self.show_section("general")
    
    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        self.window = ctk.CTkToplevel()
        self.window.title("🎛️ لوحة التحكم المركزية - برنامج المحاسبة")
        self.window.geometry("1400x900")
        self.window.configure(fg_color=WARM_COLORS['warm_white'])
        
        # جعل النافذة في وضع ملء الشاشة
        self.window.state('zoomed')
        
        # تكوين الشبكة
        self.window.grid_columnconfigure(1, weight=1)
        self.window.grid_rowconfigure(1, weight=1)
        
        if self.parent:
            self.window.transient(self.parent)
            self.window.grab_set()
    
    def create_interface(self):
        """إنشاء واجهة لوحة التحكم"""
        # الشريط العلوي
        self.create_top_bar()
        
        # الشريط الجانبي
        self.create_sidebar()
        
        # المنطقة الرئيسية
        self.create_main_area()
        
        # الشريط السفلي
        self.create_bottom_bar()
    
    def create_top_bar(self):
        """إنشاء الشريط العلوي"""
        top_frame = ctk.CTkFrame(
            self.window,
            height=80,
            fg_color=WARM_COLORS['coral'],
            corner_radius=0
        )
        top_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=0, pady=0)
        top_frame.grid_propagate(False)
        
        # شعار وعنوان
        title_frame = ctk.CTkFrame(top_frame, fg_color="transparent")
        title_frame.pack(side="right", fill="y", padx=20)
        
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            title_frame,
            text="🎛️ لوحة التحكم المركزية",
            font=(FONTS['arabic'], 28, "bold"),
            text_color="white"
        )
        title_label.pack(side="top", pady=(15, 5))
        
        # العنوان الفرعي
        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="إدارة شاملة لجميع إعدادات النظام",
            font=(FONTS['arabic'], 14),
            text_color=WARM_COLORS['cream']
        )
        subtitle_label.pack(side="top")
        
        # معلومات الشركة على اليسار
        company_frame = ctk.CTkFrame(top_frame, fg_color="transparent")
        company_frame.pack(side="left", fill="y", padx=20)
        
        company_label = ctk.CTkLabel(
            company_frame,
            text="شركة ست الكل للمحاسبة",
            font=(FONTS['arabic'], 16, "bold"),
            text_color="white"
        )
        company_label.pack(expand=True)
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي للتنقل"""
        self.sidebar = ctk.CTkFrame(
            self.window,
            width=280,
            fg_color=WARM_COLORS['soft_cream'],
            corner_radius=0
        )
        self.sidebar.grid(row=1, column=0, sticky="nsew", padx=0, pady=0)
        self.sidebar.grid_propagate(False)
        
        # عنوان الشريط الجانبي
        sidebar_title = ctk.CTkLabel(
            self.sidebar,
            text="🧭 أقسام التحكم",
            font=(FONTS['arabic'], 18, "bold"),
            text_color=WARM_COLORS['dark_coral']
        )
        sidebar_title.pack(pady=(20, 10))
        
        # أقسام التحكم
        sections = [
            {
                'key': 'general',
                'title': 'الإعدادات العامة',
                'icon': '🧩',
                'color': WARM_COLORS['coral'],
                'description': 'اللغة، التاريخ، الشعار'
            },
            {
                'key': 'users',
                'title': 'المستخدمون والصلاحيات',
                'icon': '👥',
                'color': WARM_COLORS['sunset'],
                'description': 'إدارة المستخدمين'
            },
            {
                'key': 'invoices',
                'title': 'إعدادات الفواتير',
                'icon': '🧾',
                'color': WARM_COLORS['golden'],
                'description': 'بيع، شراء، POS'
            },
            {
                'key': 'payroll',
                'title': 'الرواتب والضرائب',
                'icon': '💰',
                'color': WARM_COLORS['mint'],
                'description': 'إعدادات الرواتب'
            },
            {
                'key': 'warehouses',
                'title': 'إعدادات المخازن',
                'icon': '🏪',
                'color': WARM_COLORS['sky'],
                'description': 'إدارة المخازن'
            },
            {
                'key': 'modules',
                'title': 'التحكم في الموديلات',
                'icon': '🔧',
                'color': WARM_COLORS['rose'],
                'description': 'إظهار/إخفاء الميزات'
            },
            {
                'key': 'backup',
                'title': 'النسخ الاحتياطي',
                'icon': '💾',
                'color': WARM_COLORS['peach'],
                'description': 'حفظ واستعادة البيانات'
            },
            {
                'key': 'import_export',
                'title': 'استيراد من Excel',
                'icon': '📊',
                'color': WARM_COLORS['turquoise'],
                'description': 'إدارة البيانات'
            },
            {
                'key': 'appearance',
                'title': 'تخصيص الواجهة',
                'icon': '🎨',
                'color': WARM_COLORS['violet'],
                'description': 'الألوان والخطوط'
            },
            {
                'key': 'security',
                'title': 'نظام الأمان',
                'icon': '🛡️',
                'color': WARM_COLORS['lavender'],
                'description': 'الحماية والمراقبة'
            },
            {
                'key': 'numbering',
                'title': 'الأرقام التسلسلية',
                'icon': '🔢',
                'color': WARM_COLORS['coral'],
                'description': 'توليد الأرقام التلقائي'
            }
        ]
        
        # إنشاء أزرار الأقسام
        for section in sections:
            self.create_sidebar_button(section)
    
    def create_sidebar_button(self, section_info):
        """إنشاء زر في الشريط الجانبي"""
        button_frame = ctk.CTkFrame(
            self.sidebar,
            height=70,
            fg_color="transparent"
        )
        button_frame.pack(fill="x", padx=15, pady=5)
        button_frame.pack_propagate(False)
        
        # الزر الرئيسي
        button = ctk.CTkButton(
            button_frame,
            text="",
            height=70,
            fg_color=section_info['color'],
            hover_color=self.lighten_color(section_info['color']),
            corner_radius=12,
            command=lambda: self.show_section(section_info['key'])
        )
        button.pack(fill="both", expand=True)
        
        # محتوى الزر
        content_frame = ctk.CTkFrame(button, fg_color="transparent")
        content_frame.place(relx=0, rely=0, relwidth=1, relheight=1)
        
        # الأيقونة
        icon_label = ctk.CTkLabel(
            content_frame,
            text=section_info['icon'],
            font=("Segoe UI Emoji", 24),
            text_color="white"
        )
        icon_label.pack(side="right", padx=(0, 15), pady=10)
        
        # النص
        text_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        text_frame.pack(side="right", fill="both", expand=True, padx=(15, 0))
        
        title_label = ctk.CTkLabel(
            text_frame,
            text=section_info['title'],
            font=(FONTS['arabic'], 14, "bold"),
            text_color="white",
            anchor="e"
        )
        title_label.pack(anchor="e", pady=(8, 2))
        
        desc_label = ctk.CTkLabel(
            text_frame,
            text=section_info['description'],
            font=(FONTS['arabic'], 10),
            text_color=WARM_COLORS['cream'],
            anchor="e"
        )
        desc_label.pack(anchor="e")
        
        # حفظ مرجع الزر
        self.sidebar_buttons[section_info['key']] = button
    
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية لعرض الإعدادات"""
        self.main_area = ctk.CTkFrame(
            self.window,
            fg_color=WARM_COLORS['warm_white'],
            corner_radius=0
        )
        self.main_area.grid(row=1, column=1, sticky="nsew", padx=0, pady=0)
        
        # إطار المحتوى القابل للتمرير
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self.main_area,
            fg_color="transparent"
        )
        self.scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    def create_bottom_bar(self):
        """إنشاء الشريط السفلي"""
        bottom_frame = ctk.CTkFrame(
            self.window,
            height=60,
            fg_color=WARM_COLORS['mint_bg'],
            corner_radius=0
        )
        bottom_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=0, pady=0)
        bottom_frame.grid_propagate(False)
        
        # أزرار الإجراءات
        buttons_frame = ctk.CTkFrame(bottom_frame, fg_color="transparent")
        buttons_frame.pack(side="left", padx=20, pady=10)
        
        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ جميع الإعدادات",
            font=(FONTS['arabic'], 14, "bold"),
            fg_color=WARM_COLORS['mint'],
            hover_color=self.lighten_color(WARM_COLORS['mint']),
            height=40,
            width=180,
            command=self.save_all_settings
        )
        save_btn.pack(side="right", padx=5)
        
        # زر الاستعادة
        restore_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 استعادة الافتراضي",
            font=(FONTS['arabic'], 14, "bold"),
            fg_color=WARM_COLORS['sunset'],
            hover_color=self.lighten_color(WARM_COLORS['sunset']),
            height=40,
            width=180,
            command=self.restore_defaults
        )
        restore_btn.pack(side="right", padx=5)
        
        # زر التجربة
        test_btn = ctk.CTkButton(
            buttons_frame,
            text="🎯 تجربة الإعدادات",
            font=(FONTS['arabic'], 14, "bold"),
            fg_color=WARM_COLORS['turquoise'],
            hover_color=self.lighten_color(WARM_COLORS['turquoise']),
            height=40,
            width=180,
            command=self.test_settings
        )
        test_btn.pack(side="right", padx=5)
        
        # معلومات الحالة
        status_frame = ctk.CTkFrame(bottom_frame, fg_color="transparent")
        status_frame.pack(side="right", padx=20, pady=10)
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="✅ جاهز للتحكم",
            font=(FONTS['arabic'], 12),
            text_color=WARM_COLORS['deep_teal']
        )
        self.status_label.pack()
    
    def lighten_color(self, color):
        """تفتيح اللون للتأثير عند التمرير"""
        # تحويل بسيط لتفتيح اللون
        if color.startswith('#'):
            hex_color = color[1:]
            rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
            lighter_rgb = tuple(min(255, int(c * 1.2)) for c in rgb)
            return f"#{lighter_rgb[0]:02x}{lighter_rgb[1]:02x}{lighter_rgb[2]:02x}"
        return color

    def show_section(self, section_key):
        """عرض قسم معين من الإعدادات"""
        # تحديث الزر النشط
        for key, button in self.sidebar_buttons.items():
            if key == section_key:
                button.configure(fg_color=WARM_COLORS['coral'])
            else:
                # إعادة اللون الأصلي للأزرار الأخرى
                section_colors = {
                    'general': WARM_COLORS['coral'],
                    'users': WARM_COLORS['sunset'],
                    'invoices': WARM_COLORS['golden'],
                    'payroll': WARM_COLORS['mint'],
                    'warehouses': WARM_COLORS['sky'],
                    'modules': WARM_COLORS['rose'],
                    'backup': WARM_COLORS['peach'],
                    'import_export': WARM_COLORS['turquoise'],
                    'appearance': WARM_COLORS['violet'],
                    'security': WARM_COLORS['lavender'],
                    'numbering': WARM_COLORS['coral']
                }
                button.configure(fg_color=section_colors.get(key, WARM_COLORS['coral']))

        # مسح المحتوى الحالي
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # عرض القسم المطلوب
        self.current_section = section_key

        if section_key == "general":
            self.create_general_settings()
        elif section_key == "users":
            self.create_users_settings()
        elif section_key == "invoices":
            self.create_invoices_settings()
        elif section_key == "payroll":
            self.create_payroll_settings()
        elif section_key == "warehouses":
            self.create_warehouses_settings()
        elif section_key == "modules":
            self.create_modules_settings()
        elif section_key == "backup":
            self.create_backup_settings()
        elif section_key == "import_export":
            self.create_import_export_settings()
        elif section_key == "appearance":
            self.create_appearance_settings()
        elif section_key == "security":
            self.create_security_settings()
        elif section_key == "numbering":
            self.create_numbering_settings()

    def create_section_header(self, title, icon, description, color):
        """إنشاء رأس القسم"""
        header_frame = ctk.CTkFrame(
            self.scrollable_frame,
            height=100,
            fg_color=color,
            corner_radius=15
        )
        header_frame.pack(fill="x", pady=(0, 20))
        header_frame.pack_propagate(False)

        # الأيقونة
        icon_label = ctk.CTkLabel(
            header_frame,
            text=icon,
            font=("Segoe UI Emoji", 36),
            text_color="white"
        )
        icon_label.pack(side="right", padx=30, pady=20)

        # النص
        text_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        text_frame.pack(side="right", fill="both", expand=True, padx=(20, 0))

        title_label = ctk.CTkLabel(
            text_frame,
            text=title,
            font=(FONTS['arabic'], 24, "bold"),
            text_color="white",
            anchor="e"
        )
        title_label.pack(anchor="e", pady=(15, 5))

        desc_label = ctk.CTkLabel(
            text_frame,
            text=description,
            font=(FONTS['arabic'], 14),
            text_color=WARM_COLORS['cream'],
            anchor="e"
        )
        desc_label.pack(anchor="e")

    def create_settings_card(self, title, content_func, color=None):
        """إنشاء بطاقة إعدادات"""
        if color is None:
            color = WARM_COLORS['light_peach']

        card_frame = ctk.CTkFrame(
            self.scrollable_frame,
            fg_color=color,
            corner_radius=12
        )
        card_frame.pack(fill="x", pady=10)

        # عنوان البطاقة
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=(FONTS['arabic'], 16, "bold"),
            text_color=WARM_COLORS['dark_coral'],
            anchor="e"
        )
        title_label.pack(anchor="e", padx=20, pady=(15, 10))

        # محتوى البطاقة
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=20, pady=(0, 15))

        # استدعاء دالة المحتوى
        content_func(content_frame)

        return card_frame

    def create_general_settings(self):
        """إنشاء إعدادات عامة"""
        self.create_section_header(
            "الإعدادات العامة",
            "🧩",
            "إعدادات اللغة، التاريخ، الشعار، وشكل الواجهة",
            WARM_COLORS['coral']
        )

        # بطاقة معلومات الشركة
        def company_info_content(parent):
            # اسم الشركة
            self.create_input_field(parent, "اسم الشركة:", "company_name", "شركة ست الكل للمحاسبة")

            # السجل التجاري
            self.create_input_field(parent, "السجل التجاري:", "commercial_register", "")

            # الهاتف
            self.create_input_field(parent, "رقم الهاتف:", "phone", "")

            # البريد الإلكتروني
            self.create_input_field(parent, "البريد الإلكتروني:", "email", "")

            # العنوان
            self.create_textarea_field(parent, "عنوان الشركة:", "address", "")

            # الشعار
            self.create_file_field(parent, "شعار الشركة:", "company_logo", "اختيار صورة")

        self.create_settings_card("📋 معلومات الشركة", company_info_content, WARM_COLORS['light_peach'])

        # بطاقة الإعدادات الأساسية
        def basic_settings_content(parent):
            # اللغة الافتراضية
            self.create_dropdown_field(parent, "اللغة الافتراضية:", "default_language",
                                     ["العربية", "English"], "العربية")

            # تنسيق التاريخ
            self.create_dropdown_field(parent, "تنسيق التاريخ:", "date_format",
                                     ["DD/MM/YYYY", "MM/DD/YYYY", "YYYY-MM-DD"], "DD/MM/YYYY")

            # العملة الافتراضية
            self.create_dropdown_field(parent, "العملة الافتراضية:", "default_currency",
                                     ["ريال سعودي", "درهم إماراتي", "دينار كويتي", "دولار أمريكي"], "ريال سعودي")

            # رمز العملة
            self.create_input_field(parent, "رمز العملة:", "currency_symbol", "ر.س")

            # الوضع الليلي
            self.create_switch_field(parent, "تفعيل الوضع الليلي:", "dark_mode", False)

            # الأصوات
            self.create_switch_field(parent, "تفعيل الأصوات:", "sounds_enabled", True)

        self.create_settings_card("⚙️ الإعدادات الأساسية", basic_settings_content, WARM_COLORS['mint_bg'])

    def create_input_field(self, parent, label, key, default_value=""):
        """إنشاء حقل إدخال نص"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)

        # التسمية
        label_widget = ctk.CTkLabel(
            field_frame,
            text=label,
            font=(FONTS['arabic'], 12, "bold"),
            text_color=WARM_COLORS['deep_teal'],
            anchor="e",
            width=150
        )
        label_widget.pack(side="right", padx=(0, 10))

        # حقل الإدخال
        entry = ctk.CTkEntry(
            field_frame,
            font=(FONTS['arabic'], 12),
            height=35,
            fg_color="white",
            border_color=WARM_COLORS['coral'],
            border_width=2
        )
        entry.pack(side="right", fill="x", expand=True)
        entry.insert(0, self.current_settings.get(key, default_value))

        # حفظ مرجع الحقل
        setattr(self, f"field_{key}", entry)

        return entry

    def create_dropdown_field(self, parent, label, key, options, default_value=""):
        """إنشاء حقل قائمة منسدلة"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)

        # التسمية
        label_widget = ctk.CTkLabel(
            field_frame,
            text=label,
            font=(FONTS['arabic'], 12, "bold"),
            text_color=WARM_COLORS['deep_teal'],
            anchor="e",
            width=150
        )
        label_widget.pack(side="right", padx=(0, 10))

        # القائمة المنسدلة
        dropdown = ctk.CTkComboBox(
            field_frame,
            values=options,
            font=(FONTS['arabic'], 12),
            height=35,
            fg_color="white",
            border_color=WARM_COLORS['coral'],
            border_width=2,
            button_color=WARM_COLORS['coral'],
            button_hover_color=self.lighten_color(WARM_COLORS['coral'])
        )
        dropdown.pack(side="right", fill="x", expand=True)
        dropdown.set(self.current_settings.get(key, default_value))

        # حفظ مرجع الحقل
        setattr(self, f"field_{key}", dropdown)

        return dropdown

    def create_switch_field(self, parent, label, key, default_value=False):
        """إنشاء حقل مفتاح تشغيل/إيقاف"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)

        # التسمية
        label_widget = ctk.CTkLabel(
            field_frame,
            text=label,
            font=(FONTS['arabic'], 12, "bold"),
            text_color=WARM_COLORS['deep_teal'],
            anchor="e",
            width=150
        )
        label_widget.pack(side="right", padx=(0, 10))

        # المفتاح
        switch = ctk.CTkSwitch(
            field_frame,
            text="",
            font=(FONTS['arabic'], 12),
            progress_color=WARM_COLORS['mint'],
            button_color=WARM_COLORS['coral'],
            button_hover_color=self.lighten_color(WARM_COLORS['coral'])
        )
        switch.pack(side="right")

        # تعيين القيمة الافتراضية
        current_value = self.current_settings.get(key, default_value)
        if current_value:
            switch.select()
        else:
            switch.deselect()

        # حفظ مرجع الحقل
        setattr(self, f"field_{key}", switch)

        return switch

    def create_textarea_field(self, parent, label, key, default_value=""):
        """إنشاء حقل نص متعدد الأسطر"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)

        # التسمية
        label_widget = ctk.CTkLabel(
            field_frame,
            text=label,
            font=(FONTS['arabic'], 12, "bold"),
            text_color=WARM_COLORS['deep_teal'],
            anchor="ne",
            width=150
        )
        label_widget.pack(side="right", padx=(0, 10), anchor="n")

        # حقل النص
        textbox = ctk.CTkTextbox(
            field_frame,
            font=(FONTS['arabic'], 12),
            height=80,
            fg_color="white",
            border_color=WARM_COLORS['coral'],
            border_width=2
        )
        textbox.pack(side="right", fill="both", expand=True)
        textbox.insert("1.0", self.current_settings.get(key, default_value))

        # حفظ مرجع الحقل
        setattr(self, f"field_{key}", textbox)

        return textbox

    def create_file_field(self, parent, label, key, button_text="اختيار ملف"):
        """إنشاء حقل اختيار ملف"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)

        # التسمية
        label_widget = ctk.CTkLabel(
            field_frame,
            text=label,
            font=(FONTS['arabic'], 12, "bold"),
            text_color=WARM_COLORS['deep_teal'],
            anchor="e",
            width=150
        )
        label_widget.pack(side="right", padx=(0, 10))

        # إطار الملف
        file_frame = ctk.CTkFrame(field_frame, fg_color="transparent")
        file_frame.pack(side="right", fill="x", expand=True)

        # زر الاختيار
        file_button = ctk.CTkButton(
            file_frame,
            text=button_text,
            font=(FONTS['arabic'], 12),
            height=35,
            width=120,
            fg_color=WARM_COLORS['sunset'],
            hover_color=self.lighten_color(WARM_COLORS['sunset']),
            command=lambda: self.select_file(key)
        )
        file_button.pack(side="left", padx=(0, 10))

        # عرض المسار
        path_label = ctk.CTkLabel(
            file_frame,
            text=self.current_settings.get(key, "لم يتم اختيار ملف"),
            font=(FONTS['arabic'], 10),
            text_color=WARM_COLORS['deep_teal'],
            anchor="w"
        )
        path_label.pack(side="left", fill="x", expand=True)

        # حفظ مرجع الحقل
        setattr(self, f"field_{key}", path_label)
        setattr(self, f"button_{key}", file_button)

        return file_button, path_label

    def create_color_field(self, parent, label, key, default_value="#FFFFFF"):
        """إنشاء حقل اختيار لون"""
        field_frame = ctk.CTkFrame(parent, fg_color="transparent")
        field_frame.pack(fill="x", pady=5)

        # التسمية
        label_widget = ctk.CTkLabel(
            field_frame,
            text=label,
            font=(FONTS['arabic'], 12, "bold"),
            text_color=WARM_COLORS['deep_teal'],
            anchor="e",
            width=150
        )
        label_widget.pack(side="right", padx=(0, 10))

        # إطار اللون
        color_frame = ctk.CTkFrame(field_frame, fg_color="transparent")
        color_frame.pack(side="right", fill="x", expand=True)

        # زر اختيار اللون
        current_color = self.current_settings.get(key, default_value)
        color_button = ctk.CTkButton(
            color_frame,
            text="اختيار اللون",
            font=(FONTS['arabic'], 12),
            height=35,
            width=120,
            fg_color=current_color,
            hover_color=self.lighten_color(current_color),
            command=lambda: self.select_color(key)
        )
        color_button.pack(side="left", padx=(0, 10))

        # عرض كود اللون
        color_label = ctk.CTkLabel(
            color_frame,
            text=current_color,
            font=(FONTS['arabic'], 10),
            text_color=WARM_COLORS['deep_teal'],
            anchor="w"
        )
        color_label.pack(side="left", fill="x", expand=True)

        # حفظ مرجع الحقل
        setattr(self, f"field_{key}", color_label)
        setattr(self, f"button_{key}", color_button)

        return color_button, color_label

    def select_file(self, key):
        """اختيار ملف"""
        file_path = filedialog.askopenfilename(
            title="اختيار ملف",
            filetypes=[
                ("صور", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("جميع الملفات", "*.*")
            ]
        )
        if file_path:
            # تحديث عرض المسار
            path_label = getattr(self, f"field_{key}")
            path_label.configure(text=file_path)

            # حفظ في الإعدادات
            self.current_settings[key] = file_path

    def select_color(self, key):
        """اختيار لون"""
        current_color = self.current_settings.get(key, "#FFFFFF")
        color = colorchooser.askcolor(
            title="اختيار لون",
            initialcolor=current_color
        )
        if color[1]:  # إذا تم اختيار لون
            new_color = color[1]

            # تحديث الزر واللون
            color_button = getattr(self, f"button_{key}")
            color_label = getattr(self, f"field_{key}")

            color_button.configure(fg_color=new_color, hover_color=self.lighten_color(new_color))
            color_label.configure(text=new_color)

            # حفظ في الإعدادات
            self.current_settings[key] = new_color

    def create_numbering_settings(self):
        """إنشاء إعدادات الأرقام التسلسلية"""
        self.create_section_header(
            "الأرقام التسلسلية",
            "🔢",
            "توليد تلقائي للأرقام التسلسلية لجميع الفواتير والموظفين والعملاء",
            WARM_COLORS['turquoise']
        )

        # بطاقة إعدادات الفواتير
        def invoice_numbering_content(parent):
            self.create_input_field(parent, "بادئة فواتير البيع:", "sales_invoice_prefix", "INV")
            self.create_input_field(parent, "بادئة فواتير الشراء:", "purchase_invoice_prefix", "PUR")
            self.create_input_field(parent, "بادئة المرتجعات:", "return_prefix", "RET")
            self.create_input_field(parent, "طول الرقم التسلسلي:", "number_length", "6")
            self.create_switch_field(parent, "تضمين السنة:", "include_year", True)
            self.create_switch_field(parent, "تضمين الشهر:", "include_month", True)

        self.create_settings_card("🧾 ترقيم الفواتير", invoice_numbering_content, WARM_COLORS['light_peach'])

        # بطاقة إعدادات الموظفين والعملاء
        def entity_numbering_content(parent):
            self.create_input_field(parent, "بادئة الموظفين:", "employee_prefix", "EMP")
            self.create_input_field(parent, "بادئة العملاء:", "customer_prefix", "CUS")
            self.create_input_field(parent, "بادئة الموردين:", "supplier_prefix", "SUP")
            self.create_switch_field(parent, "ترقيم تلقائي:", "auto_numbering", True)

        self.create_settings_card("👥 ترقيم الأشخاص", entity_numbering_content, WARM_COLORS['mint_bg'])

    def create_appearance_settings(self):
        """إنشاء إعدادات تخصيص الواجهة"""
        self.create_section_header(
            "تخصيص الواجهة",
            "🎨",
            "تخصيص الألوان والخطوط وشكل الواجهة",
            WARM_COLORS['violet']
        )

        # بطاقة الألوان
        def colors_content(parent):
            self.create_color_field(parent, "اللون الأساسي:", "primary_color", WARM_COLORS['coral'])
            self.create_color_field(parent, "لون الخلفية:", "background_color", WARM_COLORS['warm_white'])
            self.create_color_field(parent, "لون العناوين:", "header_color", WARM_COLORS['deep_teal'])
            self.create_color_field(parent, "لون الأزرار:", "button_color", WARM_COLORS['mint'])
            self.create_color_field(parent, "لون النص:", "text_color", WARM_COLORS['dark_coral'])

        self.create_settings_card("🌈 الألوان", colors_content, WARM_COLORS['light_peach'])

        # بطاقة الخطوط
        def fonts_content(parent):
            font_options = ["Cairo", "Amiri", "Noto Naskh Arabic", "Tajawal", "Almarai"]
            self.create_dropdown_field(parent, "الخط الأساسي:", "main_font", font_options, "Cairo")
            self.create_dropdown_field(parent, "خط العناوين:", "header_font", font_options, "Cairo")
            self.create_dropdown_field(parent, "حجم الخط:", "font_size", ["صغير", "متوسط", "كبير", "كبير جداً"], "متوسط")
            self.create_switch_field(parent, "خط عريض للعناوين:", "bold_headers", True)

        self.create_settings_card("🔤 الخطوط", fonts_content, WARM_COLORS['mint_bg'])

    def create_backup_settings(self):
        """إنشاء إعدادات النسخ الاحتياطي"""
        self.create_section_header(
            "النسخ الاحتياطي",
            "💾",
            "إدارة النسخ الاحتياطية واستعادة النظام",
            WARM_COLORS['peach']
        )

        # بطاقة الإعدادات التلقائية
        def auto_backup_content(parent):
            self.create_switch_field(parent, "النسخ التلقائي:", "auto_backup", True)
            self.create_dropdown_field(parent, "فترة النسخ:", "backup_frequency",
                                     ["يومياً", "أسبوعياً", "شهرياً"], "يومياً")
            self.create_dropdown_field(parent, "وقت النسخ:", "backup_time",
                                     ["02:00", "03:00", "04:00", "23:00"], "02:00")
            self.create_input_field(parent, "عدد النسخ المحفوظة:", "max_backups", "30")
            self.create_switch_field(parent, "ضغط النسخ:", "compress_backups", True)

        self.create_settings_card("⏰ النسخ التلقائي", auto_backup_content, WARM_COLORS['light_peach'])

        # بطاقة إدارة النسخ
        def backup_management_content(parent):
            # أزرار الإجراءات
            buttons_frame = ctk.CTkFrame(parent, fg_color="transparent")
            buttons_frame.pack(fill="x", pady=10)

            # زر إنشاء نسخة احتياطية
            backup_btn = ctk.CTkButton(
                buttons_frame,
                text="💾 إنشاء نسخة احتياطية الآن",
                font=(FONTS['arabic'], 12, "bold"),
                fg_color=WARM_COLORS['mint'],
                hover_color=self.lighten_color(WARM_COLORS['mint']),
                height=40,
                command=self.create_backup
            )
            backup_btn.pack(side="right", padx=5)

            # زر استعادة النسخة
            restore_btn = ctk.CTkButton(
                buttons_frame,
                text="🔄 استعادة من نسخة احتياطية",
                font=(FONTS['arabic'], 12, "bold"),
                fg_color=WARM_COLORS['sunset'],
                hover_color=self.lighten_color(WARM_COLORS['sunset']),
                height=40,
                command=self.restore_backup
            )
            restore_btn.pack(side="right", padx=5)

        self.create_settings_card("🛠️ إدارة النسخ", backup_management_content, WARM_COLORS['mint_bg'])

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            backup_dir = PROJECT_ROOT / "backups"
            backup_dir.mkdir(exist_ok=True)

            # اسم الملف مع التاريخ والوقت
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"backup_{timestamp}.db"

            # نسخ قاعدة البيانات
            import shutil
            shutil.copy2(DATABASE_PATH, backup_file)

            self.update_status(f"✅ تم إنشاء النسخة الاحتياطية: {backup_file.name}")
            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح\n{backup_file}")

        except Exception as e:
            self.update_status(f"❌ خطأ في إنشاء النسخة الاحتياطية")
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية:\n{str(e)}")

    def restore_backup(self):
        """استعادة من نسخة احتياطية"""
        backup_file = filedialog.askopenfilename(
            title="اختيار نسخة احتياطية",
            initialdir=PROJECT_ROOT / "backups",
            filetypes=[("قواعد البيانات", "*.db"), ("جميع الملفات", "*.*")]
        )

        if backup_file:
            result = messagebox.askyesno(
                "تأكيد الاستعادة",
                "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n"
                "سيتم استبدال البيانات الحالية بالكامل!"
            )

            if result:
                try:
                    import shutil
                    shutil.copy2(backup_file, DATABASE_PATH)

                    self.update_status("✅ تم استعادة النسخة الاحتياطية بنجاح")
                    messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح\nيُنصح بإعادة تشغيل البرنامج")

                except Exception as e:
                    self.update_status("❌ خطأ في استعادة النسخة الاحتياطية")
                    messagebox.showerror("خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية:\n{str(e)}")

    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.configure(text=message)

    def create_users_settings(self):
        """إنشاء إعدادات المستخدمين والصلاحيات"""
        self.create_section_header(
            "المستخدمون والصلاحيات",
            "👥",
            "إدارة المستخدمين والأدوار والصلاحيات",
            WARM_COLORS['sunset']
        )

        # بطاقة إعدادات الأمان
        def security_content(parent):
            self.create_input_field(parent, "الحد الأدنى لطول كلمة المرور:", "min_password_length", "6")
            self.create_switch_field(parent, "تطلب أرقام في كلمة المرور:", "require_numbers", True)
            self.create_switch_field(parent, "تطلب رموز خاصة:", "require_symbols", False)
            self.create_input_field(parent, "مدة انتهاء الجلسة (دقيقة):", "session_timeout", "60")
            self.create_input_field(parent, "عدد محاولات تسجيل الدخول:", "max_login_attempts", "3")

        self.create_settings_card("🔐 إعدادات الأمان", security_content, WARM_COLORS['light_peach'])

    def create_invoices_settings(self):
        """إنشاء إعدادات الفواتير"""
        self.create_section_header(
            "إعدادات الفواتير",
            "🧾",
            "إعدادات البيع والشراء و POS والمرتجعات والتقارير",
            WARM_COLORS['golden']
        )

        # بطاقة إعدادات الفواتير العامة
        def invoice_general_content(parent):
            self.create_dropdown_field(parent, "قالب الفاتورة:", "invoice_template",
                                     ["حديث", "كلاسيكي", "مبسط"], "حديث")
            self.create_dropdown_field(parent, "حجم الورق:", "paper_size",
                                     ["A4", "A5", "Letter"], "A4")
            self.create_switch_field(parent, "عرض الشعار في الفاتورة:", "show_logo", True)
            self.create_switch_field(parent, "طباعة تلقائية:", "auto_print", False)
            self.create_input_field(parent, "معدل الضريبة (%):", "tax_rate", "15")

        self.create_settings_card("📄 إعدادات عامة", invoice_general_content, WARM_COLORS['light_peach'])

    def create_payroll_settings(self):
        """إنشاء إعدادات الرواتب والضرائب"""
        self.create_section_header(
            "الرواتب والضرائب",
            "💰",
            "إعدادات الرواتب والضرائب والخصومات",
            WARM_COLORS['mint']
        )

        # بطاقة إعدادات الرواتب
        def payroll_content(parent):
            self.create_input_field(parent, "معدل ضريبة الدخل (%):", "income_tax_rate", "10")
            self.create_input_field(parent, "نسبة التأمينات (%):", "insurance_rate", "9")
            self.create_switch_field(parent, "حساب الإضافي تلقائياً:", "auto_overtime", True)
            self.create_input_field(parent, "ساعات العمل اليومية:", "daily_hours", "8")
            self.create_input_field(parent, "أيام العمل الأسبوعية:", "weekly_days", "5")

        self.create_settings_card("💼 إعدادات الرواتب", payroll_content, WARM_COLORS['light_peach'])

    def create_warehouses_settings(self):
        """إنشاء إعدادات المخازن"""
        self.create_section_header(
            "إعدادات المخازن",
            "🏪",
            "إدارة المخازن والمخزون والباركود",
            WARM_COLORS['sky']
        )

        # بطاقة إعدادات المخزون
        def inventory_content(parent):
            self.create_switch_field(parent, "تتبع المخزون:", "track_inventory", True)
            self.create_switch_field(parent, "تحذير نفاد المخزون:", "low_stock_alert", True)
            self.create_input_field(parent, "الحد الأدنى للمخزون:", "min_stock_level", "10")
            self.create_switch_field(parent, "استخدام الباركود:", "use_barcode", True)
            self.create_dropdown_field(parent, "طريقة تقييم المخزون:", "inventory_method",
                                     ["FIFO", "LIFO", "متوسط مرجح"], "FIFO")

        self.create_settings_card("📦 إدارة المخزون", inventory_content, WARM_COLORS['light_peach'])

    def create_modules_settings(self):
        """إنشاء إعدادات التحكم في الموديلات"""
        self.create_section_header(
            "التحكم في الموديلات",
            "🔧",
            "إظهار وإخفاء الميزات والموديلات النشطة",
            WARM_COLORS['rose']
        )

        # بطاقة الموديلات الأساسية
        def basic_modules_content(parent):
            self.create_switch_field(parent, "موديل المبيعات:", "sales_module", True)
            self.create_switch_field(parent, "موديل المشتريات:", "purchases_module", True)
            self.create_switch_field(parent, "موديل المخازن:", "inventory_module", True)
            self.create_switch_field(parent, "موديل الحسابات:", "accounts_module", True)
            self.create_switch_field(parent, "موديل الرواتب:", "payroll_module", True)

        self.create_settings_card("🧩 الموديلات الأساسية", basic_modules_content, WARM_COLORS['light_peach'])

        # بطاقة الموديلات المتقدمة
        def advanced_modules_content(parent):
            self.create_switch_field(parent, "نقاط البيع (POS):", "pos_module", True)
            self.create_switch_field(parent, "التقارير المتقدمة:", "advanced_reports", True)
            self.create_switch_field(parent, "إدارة العملاء:", "crm_module", True)
            self.create_switch_field(parent, "الباركود:", "barcode_module", True)
            self.create_switch_field(parent, "التكامل مع Excel:", "excel_integration", True)

        self.create_settings_card("⚡ الموديلات المتقدمة", advanced_modules_content, WARM_COLORS['mint_bg'])

    def create_import_export_settings(self):
        """إنشاء إعدادات استيراد وتصدير البيانات"""
        self.create_section_header(
            "استيراد من Excel",
            "📊",
            "إدارة استيراد وتصدير البيانات من وإلى Excel",
            WARM_COLORS['turquoise']
        )

        # بطاقة إعدادات الاستيراد
        def import_content(parent):
            self.create_switch_field(parent, "السماح بالاستيراد:", "allow_import", True)
            self.create_switch_field(parent, "التحقق من البيانات:", "validate_data", True)
            self.create_switch_field(parent, "إنشاء نسخة احتياطية قبل الاستيراد:", "backup_before_import", True)
            self.create_input_field(parent, "الحد الأقصى للصفوف:", "max_import_rows", "1000")

        self.create_settings_card("📥 إعدادات الاستيراد", import_content, WARM_COLORS['light_peach'])

    def create_security_settings(self):
        """إنشاء إعدادات نظام الأمان"""
        self.create_section_header(
            "نظام الأمان",
            "🛡️",
            "الحماية والمراقبة وسجل العمليات",
            WARM_COLORS['lavender']
        )

        # بطاقة إعدادات الأمان
        def security_content(parent):
            self.create_switch_field(parent, "تسجيل العمليات:", "log_operations", True)
            self.create_switch_field(parent, "مراقبة تسجيل الدخول:", "monitor_login", True)
            self.create_switch_field(parent, "تشفير البيانات الحساسة:", "encrypt_sensitive", False)
            self.create_input_field(parent, "مدة حفظ السجلات (يوم):", "log_retention_days", "90")

        self.create_settings_card("🔒 إعدادات الأمان", security_content, WARM_COLORS['light_peach'])

        # بطاقة إجراءات الأمان
        def security_actions_content(parent):
            # أزرار الإجراءات الخطيرة
            buttons_frame = ctk.CTkFrame(parent, fg_color="transparent")
            buttons_frame.pack(fill="x", pady=10)

            # زر ضبط المصنع
            factory_reset_btn = ctk.CTkButton(
                buttons_frame,
                text="⚠️ ضبط المصنع",
                font=(FONTS['arabic'], 12, "bold"),
                fg_color=WARM_COLORS['coral'],
                hover_color=self.lighten_color(WARM_COLORS['coral']),
                height=40,
                command=self.factory_reset
            )
            factory_reset_btn.pack(side="right", padx=5)

            # زر عرض سجل العمليات
            view_log_btn = ctk.CTkButton(
                buttons_frame,
                text="📋 عرض سجل العمليات",
                font=(FONTS['arabic'], 12, "bold"),
                fg_color=WARM_COLORS['turquoise'],
                hover_color=self.lighten_color(WARM_COLORS['turquoise']),
                height=40,
                command=self.view_operations_log
            )
            view_log_btn.pack(side="right", padx=5)

        self.create_settings_card("⚡ إجراءات الأمان", security_actions_content, WARM_COLORS['mint_bg'])

    def factory_reset(self):
        """ضبط المصنع - حذف جميع البيانات"""
        result = messagebox.askyesnocancel(
            "تحذير - ضبط المصنع",
            "⚠️ تحذير شديد ⚠️\n\n"
            "هذا الإجراء سيحذف جميع البيانات نهائياً!\n"
            "• جميع الفواتير والمعاملات\n"
            "• بيانات العملاء والموردين\n"
            "• المخزون والمنتجات\n"
            "• الإعدادات المخصصة\n\n"
            "هل أنت متأكد تماماً من المتابعة؟"
        )

        if result:
            # طلب تأكيد إضافي
            confirm = messagebox.askstring(
                "تأكيد نهائي",
                "اكتب 'حذف نهائي' للتأكيد:",
                show='*'
            )

            if confirm == "حذف نهائي":
                try:
                    # إنشاء نسخة احتياطية أولاً
                    self.create_backup()

                    # حذف قاعدة البيانات وإعادة إنشائها
                    if DATABASE_PATH.exists():
                        DATABASE_PATH.unlink()

                    # إعادة إنشاء قاعدة البيانات الفارغة
                    conn = sqlite3.connect(DATABASE_PATH)
                    conn.close()

                    self.update_status("✅ تم ضبط المصنع بنجاح")
                    messagebox.showinfo("تم", "تم ضبط المصنع بنجاح\nيجب إعادة تشغيل البرنامج")

                except Exception as e:
                    self.update_status("❌ خطأ في ضبط المصنع")
                    messagebox.showerror("خطأ", f"حدث خطأ في ضبط المصنع:\n{str(e)}")
            else:
                messagebox.showinfo("تم الإلغاء", "تم إلغاء عملية ضبط المصنع")

    def view_operations_log(self):
        """عرض سجل العمليات"""
        # إنشاء نافذة جديدة لعرض السجل
        log_window = ctk.CTkToplevel(self.window)
        log_window.title("📋 سجل العمليات")
        log_window.geometry("800x600")
        log_window.configure(fg_color=WARM_COLORS['warm_white'])

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            log_window,
            text="📋 سجل العمليات والأنشطة",
            font=(FONTS['arabic'], 20, "bold"),
            text_color=WARM_COLORS['deep_teal']
        )
        title_label.pack(pady=20)

        # منطقة عرض السجل
        log_text = ctk.CTkTextbox(
            log_window,
            font=(FONTS['arabic'], 12),
            fg_color="white",
            border_color=WARM_COLORS['coral'],
            border_width=2
        )
        log_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # إدراج بيانات وهمية للسجل
        sample_log = """
🕐 2025-07-22 10:30:15 - تسجيل دخول المستخدم: admin
🕐 2025-07-22 10:31:22 - إنشاء فاتورة بيع رقم: INV-2025-001
🕐 2025-07-22 10:35:45 - تعديل بيانات العميل: أحمد محمد
🕐 2025-07-22 10:40:12 - إضافة منتج جديد: لابتوب HP
🕐 2025-07-22 10:45:33 - إنشاء نسخة احتياطية تلقائية
🕐 2025-07-22 10:50:18 - تحديث إعدادات النظام
🕐 2025-07-22 11:00:05 - تسجيل خروج المستخدم: admin
        """
        log_text.insert("1.0", sample_log.strip())
        log_text.configure(state="disabled")

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.current_settings = json.load(f)
            else:
                # إعدادات افتراضية
                self.current_settings = self.get_default_settings()

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            self.current_settings = self.get_default_settings()

    def get_default_settings(self):
        """الحصول على الإعدادات الافتراضية"""
        return {
            # معلومات الشركة
            'company_name': 'شركة ست الكل للمحاسبة',
            'commercial_register': '',
            'phone': '',
            'email': '',
            'address': '',
            'company_logo': '',

            # الإعدادات الأساسية
            'default_language': 'العربية',
            'date_format': 'DD/MM/YYYY',
            'default_currency': 'ريال سعودي',
            'currency_symbol': 'ر.س',
            'dark_mode': False,
            'sounds_enabled': True,

            # الأرقام التسلسلية
            'sales_invoice_prefix': 'INV',
            'purchase_invoice_prefix': 'PUR',
            'return_prefix': 'RET',
            'number_length': '6',
            'include_year': True,
            'include_month': True,
            'employee_prefix': 'EMP',
            'customer_prefix': 'CUS',
            'supplier_prefix': 'SUP',
            'auto_numbering': True,

            # الألوان
            'primary_color': WARM_COLORS['coral'],
            'background_color': WARM_COLORS['warm_white'],
            'header_color': WARM_COLORS['deep_teal'],
            'button_color': WARM_COLORS['mint'],
            'text_color': WARM_COLORS['dark_coral'],

            # الخطوط
            'main_font': 'Cairo',
            'header_font': 'Cairo',
            'font_size': 'متوسط',
            'bold_headers': True,

            # النسخ الاحتياطي
            'auto_backup': True,
            'backup_frequency': 'يومياً',
            'backup_time': '02:00',
            'max_backups': '30',
            'compress_backups': True,

            # الأمان
            'min_password_length': '6',
            'require_numbers': True,
            'require_symbols': False,
            'session_timeout': '60',
            'max_login_attempts': '3',

            # الفواتير
            'invoice_template': 'حديث',
            'paper_size': 'A4',
            'show_logo': True,
            'auto_print': False,
            'tax_rate': '15',

            # الرواتب
            'income_tax_rate': '10',
            'insurance_rate': '9',
            'auto_overtime': True,
            'daily_hours': '8',
            'weekly_days': '5',

            # المخازن
            'track_inventory': True,
            'low_stock_alert': True,
            'min_stock_level': '10',
            'use_barcode': True,
            'inventory_method': 'FIFO',

            # الموديلات
            'sales_module': True,
            'purchases_module': True,
            'inventory_module': True,
            'accounts_module': True,
            'payroll_module': True,
            'pos_module': True,
            'advanced_reports': True,
            'crm_module': True,
            'barcode_module': True,
            'excel_integration': True,

            # الاستيراد والتصدير
            'allow_import': True,
            'validate_data': True,
            'backup_before_import': True,
            'max_import_rows': '1000',

            # الأمان المتقدم
            'log_operations': True,
            'monitor_login': True,
            'encrypt_sensitive': False,
            'log_retention_days': '90'
        }

    def save_all_settings(self):
        """حفظ جميع الإعدادات"""
        try:
            # جمع القيم من جميع الحقول
            self.collect_all_field_values()

            # إنشاء مجلد الإعدادات إذا لم يكن موجوداً
            self.settings_file.parent.mkdir(exist_ok=True)

            # حفظ الإعدادات في ملف JSON
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, ensure_ascii=False, indent=2)

            self.update_status("✅ تم حفظ جميع الإعدادات بنجاح")
            messagebox.showinfo("تم الحفظ", "تم حفظ جميع الإعدادات بنجاح!")

        except Exception as e:
            self.update_status("❌ خطأ في حفظ الإعدادات")
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ الإعدادات:\n{str(e)}")

    def collect_all_field_values(self):
        """جمع القيم من جميع الحقول"""
        # البحث عن جميع الحقول المحفوظة
        for attr_name in dir(self):
            if attr_name.startswith('field_'):
                field_key = attr_name[6:]  # إزالة 'field_' من البداية
                field_widget = getattr(self, attr_name)

                try:
                    if isinstance(field_widget, ctk.CTkEntry):
                        # حقل نص
                        self.current_settings[field_key] = field_widget.get()
                    elif isinstance(field_widget, ctk.CTkComboBox):
                        # قائمة منسدلة
                        self.current_settings[field_key] = field_widget.get()
                    elif isinstance(field_widget, ctk.CTkSwitch):
                        # مفتاح تشغيل/إيقاف
                        self.current_settings[field_key] = field_widget.get() == 1
                    elif isinstance(field_widget, ctk.CTkTextbox):
                        # حقل نص متعدد الأسطر
                        self.current_settings[field_key] = field_widget.get("1.0", "end-1c")
                    elif isinstance(field_widget, ctk.CTkLabel):
                        # حقل ملف أو لون (يحفظ النص المعروض)
                        self.current_settings[field_key] = field_widget.cget("text")
                except Exception as e:
                    print(f"خطأ في جمع قيمة الحقل {field_key}: {e}")

    def restore_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        result = messagebox.askyesno(
            "استعادة الافتراضي",
            "هل أنت متأكد من استعادة جميع الإعدادات إلى القيم الافتراضية؟\n"
            "سيتم فقدان جميع التخصيصات الحالية!"
        )

        if result:
            try:
                # استعادة الإعدادات الافتراضية
                self.current_settings = self.get_default_settings()

                # حذف ملف الإعدادات المخصص
                if self.settings_file.exists():
                    self.settings_file.unlink()

                # إعادة عرض القسم الحالي لتحديث الواجهة
                self.show_section(self.current_section)

                self.update_status("✅ تم استعادة الإعدادات الافتراضية")
                messagebox.showinfo("تم", "تم استعادة الإعدادات الافتراضية بنجاح!")

            except Exception as e:
                self.update_status("❌ خطأ في استعادة الإعدادات")
                messagebox.showerror("خطأ", f"حدث خطأ في استعادة الإعدادات:\n{str(e)}")

    def test_settings(self):
        """تجربة الإعدادات قبل الحفظ"""
        try:
            # جمع القيم الحالية
            self.collect_all_field_values()

            # إنشاء نافذة معاينة
            preview_window = ctk.CTkToplevel(self.window)
            preview_window.title("🎯 معاينة الإعدادات")
            preview_window.geometry("600x400")
            preview_window.configure(fg_color=self.current_settings.get('background_color', WARM_COLORS['warm_white']))

            # عنوان المعاينة
            title_label = ctk.CTkLabel(
                preview_window,
                text="🎯 معاينة الإعدادات الجديدة",
                font=(self.current_settings.get('main_font', 'Cairo'), 20, "bold"),
                text_color=self.current_settings.get('header_color', WARM_COLORS['deep_teal'])
            )
            title_label.pack(pady=20)

            # عرض بعض الإعدادات المهمة
            info_frame = ctk.CTkFrame(
                preview_window,
                fg_color=self.current_settings.get('primary_color', WARM_COLORS['coral']),
                corner_radius=15
            )
            info_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # معلومات الشركة
            company_label = ctk.CTkLabel(
                info_frame,
                text=f"🏢 {self.current_settings.get('company_name', 'اسم الشركة')}",
                font=(self.current_settings.get('main_font', 'Cairo'), 16, "bold"),
                text_color="white"
            )
            company_label.pack(pady=10)

            # اللغة والعملة
            lang_currency_label = ctk.CTkLabel(
                info_frame,
                text=f"🌐 {self.current_settings.get('default_language', 'العربية')} | "
                     f"💰 {self.current_settings.get('default_currency', 'ريال سعودي')}",
                font=(self.current_settings.get('main_font', 'Cairo'), 14),
                text_color="white"
            )
            lang_currency_label.pack(pady=5)

            # الخط وحجمه
            font_label = ctk.CTkLabel(
                info_frame,
                text=f"🔤 الخط: {self.current_settings.get('main_font', 'Cairo')} | "
                     f"الحجم: {self.current_settings.get('font_size', 'متوسط')}",
                font=(self.current_settings.get('main_font', 'Cairo'), 12),
                text_color="white"
            )
            font_label.pack(pady=5)

            # أزرار المعاينة
            buttons_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
            buttons_frame.pack(pady=20)

            # زر إعجاب
            like_btn = ctk.CTkButton(
                buttons_frame,
                text="👍 أعجبني",
                font=(self.current_settings.get('main_font', 'Cairo'), 12),
                fg_color=self.current_settings.get('button_color', WARM_COLORS['mint']),
                hover_color=self.lighten_color(self.current_settings.get('button_color', WARM_COLORS['mint'])),
                command=lambda: self.close_preview_and_save(preview_window)
            )
            like_btn.pack(side="right", padx=10)

            # زر إغلاق
            close_btn = ctk.CTkButton(
                buttons_frame,
                text="❌ إغلاق",
                font=(self.current_settings.get('main_font', 'Cairo'), 12),
                fg_color=WARM_COLORS['sunset'],
                hover_color=self.lighten_color(WARM_COLORS['sunset']),
                command=preview_window.destroy
            )
            close_btn.pack(side="right", padx=10)

            self.update_status("🎯 تم فتح معاينة الإعدادات")

        except Exception as e:
            self.update_status("❌ خطأ في معاينة الإعدادات")
            messagebox.showerror("خطأ", f"حدث خطأ في معاينة الإعدادات:\n{str(e)}")

    def close_preview_and_save(self, preview_window):
        """إغلاق المعاينة وحفظ الإعدادات"""
        preview_window.destroy()
        self.save_all_settings()


# دالة لفتح لوحة التحكم من النافذة الرئيسية
def open_central_control_panel(parent=None):
    """فتح لوحة التحكم المركزية"""
    try:
        panel = CentralControlPanel(parent)
        return panel
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في فتح لوحة التحكم:\n{str(e)}")
        return None
