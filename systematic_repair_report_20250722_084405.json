{"timestamp": "2025-07-22T08:44:03.703463", "files_processed": 36, "files_repaired": 0, "files_failed": 36, "repair_categories": {"syntax_errors": 6, "indentation_fixes": 16, "import_fixes": 0, "string_literal_fixes": 14, "bracket_fixes": 0, "code_quality_improvements": 36}, "repaired_files": [], "failed_files": ["advanced_error_analyzer.py", "advanced_error_fixer.py", "advanced_syntax_fixer.py", "comprehensive_income_formula_demo.py", "deep_comprehensive_fixer.py", "deep_import_fixer.py", "quick_pattern_fixer.py", "run_app.py", "run_fixed_app.py", "safe_start.py", "start_with_scheduler.py", "ultimate_system_fixer.py", "config\\postgresql_config.py", "core\\app_core.py", "database\\comprehensive_income_manager.py", "database\\fix_database.py", "ui\\daily_journal_window.py", "ui\\sales_analysis_window.py", "advanced_error_analyzer.py", "advanced_error_fixer.py", "advanced_syntax_fixer.py", "comprehensive_income_formula_demo.py", "deep_comprehensive_fixer.py", "deep_import_fixer.py", "quick_pattern_fixer.py", "run_app.py", "run_fixed_app.py", "safe_start.py", "start_with_scheduler.py", "ultimate_system_fixer.py", "config\\postgresql_config.py", "core\\app_core.py", "database\\comprehensive_income_manager.py", "database\\fix_database.py", "ui\\daily_journal_window.py", "ui\\sales_analysis_window.py"], "performance_improvements": ["مراجعة وتقسيم الملف الكبير: advanced_settings_window.py (123.4 KB)", "مراجعة وتقسيم الملف الكبير: main_window.py (99.0 KB)", "إزالة الاستيرادات المكررة في: comprehensive_income_formula_demo.py", "إزالة الاستيرادات المكررة في: comprehensive_systematic_audit.py", "إزالة الاستيرادات المكررة في: ultimate_comprehensive_diagnostic.py", "إزالة الاستيرادات المكررة في: database\\hybrid_database_manager.py", "إزالة الاستيرادات المكررة في: services\\postgresql_sales_manager.py", "إزالة الاستيرادات المكررة في: services\\purchases_manager.py", "إزالة الاستيرادات المكررة في: ui\\add_items_window.py", "إزالة الاستيرادات المكررة في: ui\\advanced_financial_reports_window.py", "إزالة الاستيرادات المكررة في: ui\\daily_journal_window.py", "إزالة الاستيرادات المكررة في: ui\\main_window.py", "إزالة الاستيرادات المكررة في: ui\\sales_analysis_window.py", "إزالة الاستيرادات المكررة في: ui\\warehouses_management_window.py"]}