# 🎛️ لوحة التحكم الشاملة - دليل المستخدم

## نظرة عامة

لوحة التحكم الشاملة هي وحدة إدارية احترافية مصممة خصيصاً لبرنامج المحاسبة العربي. تتيح للمديرين والمسؤولين إدارة جميع جوانب النظام من مكان واحد بواجهة عربية RTL حديثة وسهلة الاستخدام.

## 🌟 الميزات الرئيسية

### 🎨 التصميم والواجهة
- **واجهة عربية RTL**: تصميم احترافي يدعم اللغة العربية بالكامل
- **ألوان حديثة**: نظام ألوان متدرج وجذاب
- **تصميم متجاوب**: يتكيف مع أحجام الشاشات المختلفة
- **أيقونات تعبيرية**: استخدام الإيموجي لسهولة التعرف على الوظائف

### 🔧 الوظائف الأساسية
- **7 تبويبات شاملة**: تغطي جميع جوانب إدارة النظام
- **نظام إشعارات Toast**: تنبيهات فورية للعمليات
- **حفظ تلقائي**: حفظ الإعدادات في ملفات JSON
- **استعادة افتراضية**: إمكانية إعادة تعيين الإعدادات

## 📋 التبويبات المتاحة

### 1. 🧩 الإعدادات العامة
**الوصف**: إدارة معلومات الشركة والإعدادات الأساسية

**الميزات**:
- معلومات الشركة (الاسم، الهاتف، البريد الإلكتروني)
- رفع شعار الشركة
- إعدادات العملة والضريبة
- تكوين الفواتير وأنماط الطباعة

**الحقول المتاحة**:
- 🏢 اسم المؤسسة
- 📄 رقم السجل التجاري
- 📞 رقم الهاتف
- 📧 البريد الإلكتروني
- 🖼️ شعار الشركة
- 🌐 اللغة الافتراضية
- 💰 العملة الرئيسية
- 💲 رمز العملة
- 📊 نسبة الضريبة
- 💸 نسبة الخصم الافتراضية

### 2. 💾 النسخ الاحتياطي
**الوصف**: إدارة النسخ الاحتياطية والاستعادة

**الميزات**:
- إنشاء نسخة احتياطية فورية
- استعادة النسخ الاحتياطية
- جدولة النسخ التلقائي
- سجل مفصل للنسخ السابقة

**العمليات المتاحة**:
- 💾 إنشاء نسخة احتياطية الآن
- 📥 استرجاع نسخة احتياطية
- 📅 جدولة النسخ التلقائي
- 📁 تحديد مجلد النسخ
- 📦 تحديد عدد النسخ المحفوظة

### 3. 👥 المستخدمون والصلاحيات
**الوصف**: إدارة المستخدمين وصلاحياتهم (قيد التطوير)

**الميزات المخططة**:
- إضافة وتعديل المستخدمين
- تحديد الأدوار والصلاحيات
- مراقبة نشاط المستخدمين
- إعدادات الأمان

### 4. 🔄 التحكم بالبيانات
**الوصف**: ضبط المصنع وإعادة تعيين البيانات (قيد التطوير)

**الميزات المخططة**:
- إعادة تعيين البيانات
- تنظيف قاعدة البيانات
- إعادة بناء الفهارس
- تحسين الأداء

### 5. 📥 استيراد وتصدير
**الوصف**: إدارة البيانات من وإلى Excel (قيد التطوير)

**الميزات المخططة**:
- استيراد البيانات من Excel
- تصدير التقارير
- قوالب جاهزة
- معالجة الأخطاء

### 6. ⚙️ إعدادات النظام
**الوصف**: تكوين النظام المتقدم (قيد التطوير)

**الميزات المخططة**:
- إعدادات الأداء
- تكوين قاعدة البيانات
- إعدادات الشبكة
- سجلات النظام

### 7. 🛡️ الأمان والحماية
**الوصف**: إعدادات الأمان والحماية (قيد التطوير)

**الميزات المخططة**:
- كلمات المرور
- التشفير
- سجلات الأمان
- إعدادات الجلسات

## 🚀 كيفية الاستخدام

### تشغيل لوحة التحكم
```python
from ui.advanced_settings_window import ComprehensiveAdminPanel

# إنشاء لوحة التحكم
admin_panel = ComprehensiveAdminPanel()
```

### الوصول للإعدادات
```python
# تحميل الإعدادات المحفوظة
settings = admin_panel.load_settings()

# حفظ إعدادات جديدة
admin_panel.save_all_settings()
```

## 📁 هيكل الملفات

```
ui/
├── advanced_settings_window.py    # لوحة التحكم الرئيسية
config/
├── admin_settings.json           # ملف الإعدادات المحفوظة
docs/
├── admin_panel_guide.md          # هذا الدليل
```

## 🎯 الاستخدام المتقدم

### تخصيص الألوان
```python
# تعديل نظام الألوان
colors = {
    'primary': '#2E7D32',      # أخضر داكن
    'secondary': '#1976D2',    # أزرق
    'success': '#4CAF50',      # أخضر فاتح
    'warning': '#FF9800',      # برتقالي
    'info': '#2196F3',         # أزرق فاتح
    'light': '#F5F5F5',        # رمادي فاتح
    'dark': '#212121',         # رمادي داكن
    'surface': '#FFFFFF'       # أبيض
}
```

### إضافة إشعارات مخصصة
```python
# عرض إشعار نجاح
admin_panel.show_toast("تم الحفظ بنجاح", "success")

# عرض إشعار خطأ
admin_panel.show_toast("حدث خطأ", "error")
```

## 🔧 التطوير والتخصيص

### إضافة تبويب جديد
1. إضافة التبويب في `create_professional_tabs()`
2. إنشاء دالة `create_new_tab()`
3. تحديث قائمة `tabs_config`

### إضافة حقل إعدادات جديد
```python
# حقل نصي
self.create_input_field(parent, "التسمية:", "key", "القيمة الافتراضية")

# قائمة منسدلة
self.create_dropdown_field(parent, "التسمية:", "key", ["خيار1", "خيار2"], "افتراضي")

# حقل رقمي
self.create_number_field(parent, "التسمية:", "key", 0, 0, 100)
```

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966-XX-XXX-XXXX
- 🌐 الموقع: www.example.com

## 📝 ملاحظات مهمة

- يتم حفظ الإعدادات تلقائياً في ملف JSON
- بعض الإعدادات تتطلب إعادة تشغيل البرنامج
- النسخ الاحتياطية تحفظ في مجلد منفصل
- جميع العمليات مسجلة في سجلات النظام

## 🔄 التحديثات المستقبلية

- إكمال التبويبات قيد التطوير
- إضافة المزيد من خيارات التخصيص
- تحسين الأداء والاستجابة
- إضافة المزيد من اللغات
