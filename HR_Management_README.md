# 👥 وحدة إدارة شؤون الموظفين الاحترافية

## 📋 نظرة عامة

تم تطوير وحدة احترافية متكاملة لإدارة شؤون الموظفين داخل برنامج المحاسبة العربي باستخدام Python وcustomtkinter. الوحدة مصممة لتكون جذابة، احترافية، سهلة الاستخدام، وتغطي جميع وظائف الموارد البشرية بمرونة كاملة.

## 🎯 الميزات الرئيسية

### 1. 📋 إدارة بيانات الموظفين
- **نموذج شامل للموظف**: الاسم، الرقم الوظيفي، القسم، المسمى الوظيفي، تاريخ التعيين، الرقم القومي، الهاتف، الراتب الأساسي
- **رفع صورة شخصية**: إمكانية إضافة صورة شخصية لكل موظف
- **تقسيم الموظفين**: تنظيم الموظفين حسب الأقسام والمناصب
- **البحث والفلترة**: بحث سريع بالاسم أو الرقم الوظيفي
- **إدارة الحالات**: نشط، معطل، مجمد، مستقيل

### 2. ⏰ الحضور والانصراف
- **تسجيل سريع**: واجهة سهلة لتسجيل الحضور والانصراف
- **عرض الوقت الحالي**: ساعة رقمية تتحدث في الوقت الفعلي
- **تتبع ساعات العمل**: حساب تلقائي لساعات العمل اليومية
- **إضافة ملاحظات**: إمكانية إضافة ملاحظات لكل تسجيل
- **تقارير الحضور**: عرض سجل الحضور والانصراف بالتاريخ

### 3. 💰 إدارة المرتبات
- **حساب المرتبات**: حساب تلقائي للمرتبات الشهرية
- **البدلات والخصومات**: دعم البدلات والخصومات المختلفة
- **كشوف المرتبات**: إنشاء كشوف مرتبات شهرية
- **فلترة بالشهر والسنة**: عرض المرتبات حسب فترة محددة
- **حالات الدفع**: تتبع حالة دفع المرتبات (معلق، مدفوع، ملغي)

### 4. 📊 التقارير والإحصائيات
- **تقارير الموظفين**: قائمة الموظفين وإحصائيات الأقسام
- **تقارير الحضور**: تقارير يومية وشهرية للحضور
- **تقارير المرتبات**: كشوف المرتبات وتحليل التكاليف
- **تصدير البيانات**: إمكانية تصدير التقارير (قريباً)

### 5. ⚙️ الإعدادات والتخصيص
- **إدارة الأقسام**: إضافة وتعديل أقسام الشركة
- **إدارة المناصب**: إضافة وتعديل المناصب الوظيفية
- **بنود المرتبات**: تخصيص بنود البدلات والخصومات
- **إعدادات النظام**: تخصيص إعدادات الموارد البشرية

## 🗄️ هيكل قاعدة البيانات

### جدول الموظفين (employees)
```sql
- id: معرف فريد
- employee_number: الرقم الوظيفي
- full_name: الاسم الكامل
- national_id: الرقم القومي
- phone: رقم الهاتف
- email: البريد الإلكتروني
- address: العنوان
- department_id: معرف القسم
- position_id: معرف المنصب
- hire_date: تاريخ التعيين
- birth_date: تاريخ الميلاد
- basic_salary: الراتب الأساسي
- photo_path: مسار الصورة الشخصية
- status: الحالة (active, inactive, suspended, resigned)
```

### جدول الأقسام (departments)
```sql
- id: معرف فريد
- name: اسم القسم
- description: وصف القسم
- manager_id: معرف مدير القسم
```

### جدول المناصب (positions)
```sql
- id: معرف فريد
- title: اسم المنصب
- description: وصف المنصب
- min_salary: الحد الأدنى للراتب
- max_salary: الحد الأقصى للراتب
```

### جدول الحضور (attendance)
```sql
- id: معرف فريد
- employee_id: معرف الموظف
- date: التاريخ
- check_in: وقت الحضور
- check_out: وقت الانصراف
- break_duration: مدة الاستراحة
- overtime_hours: ساعات إضافية
- status: الحالة
- notes: ملاحظات
```

### جدول المرتبات (payroll)
```sql
- id: معرف فريد
- employee_id: معرف الموظف
- month: الشهر
- year: السنة
- basic_salary: الراتب الأساسي
- total_allowances: إجمالي البدلات
- total_deductions: إجمالي الخصومات
- overtime_amount: مبلغ الساعات الإضافية
- tax_amount: مبلغ الضرائب
- insurance_amount: مبلغ التأمين
- net_salary: الراتب الصافي
- payment_date: تاريخ الدفع
- status: حالة الدفع
```

## 🎨 التصميم والواجهة

### الألوان والثيم
- **اللون الأساسي**: أخضر (#2E8B57) - متوافق مع البرنامج الرئيسي
- **اللون الثانوي**: أزرق (#4682B4)
- **لون النجاح**: أخضر فاتح (#28a745)
- **الخلفية**: رمادي فاتح (#f8f9fa)
- **السطح**: أبيض (#ffffff)

### الخطوط
- **الخط العربي**: Cairo, Amiri, Noto Naskh Arabic
- **الخط الإنجليزي**: Arial

### التخطيط
- **تبويبات رئيسية**: 5 تبويبات منظمة
- **واجهة RTL**: دعم كامل للغة العربية
- **تصميم متجاوب**: يتكيف مع أحجام الشاشة المختلفة
- **أيقونات تعبيرية**: استخدام الإيموجي لسهولة التعرف

## 🚀 كيفية الاستخدام

### 1. الوصول للنافذة
- من الشريط العلوي: اضغط على "الموظفين"
- من الأيقونات الخضراء: اضغط على أيقونة الموظفين

### 2. إضافة موظف جديد
1. اذهب إلى تبويب "بيانات الموظفين"
2. اضغط على "إضافة موظف جديد"
3. املأ البيانات المطلوبة
4. اضغط على "حفظ"

### 3. تسجيل الحضور
1. اذهب إلى تبويب "الحضور والانصراف"
2. اختر الموظف من القائمة
3. اضغط على "حضور" أو "انصراف"

### 4. حساب المرتبات
1. اذهب إلى تبويب "المرتبات"
2. اختر الشهر والسنة
3. اضغط على "حساب المرتبات"

## 🔧 المتطلبات التقنية

### المكتبات المطلوبة
```python
- customtkinter: للواجهة الرسومية
- tkinter: للعناصر الأساسية
- PIL (Pillow): لمعالجة الصور
- sqlite3: لقاعدة البيانات
- datetime: للتاريخ والوقت
```

### الملفات المطلوبة
- `ui/hr_management_window.py`: الملف الرئيسي للنافذة
- `database/hybrid_database_manager.py`: مدير قاعدة البيانات
- `themes/modern_theme.py`: ملف الثيم والألوان
- `ui/window_utils.py`: أدوات النوافذ المساعدة

## 📁 هيكل الملفات

```
ui/
├── hr_management_window.py          # النافذة الرئيسية
├── main_window.py                   # النافذة الرئيسية للبرنامج
└── window_utils.py                  # أدوات النوافذ

assets/
└── employee_photos/                 # مجلد صور الموظفين

database/
└── hybrid_database_manager.py       # مدير قاعدة البيانات

themes/
└── modern_theme.py                  # ملف الثيم
```

## 🔮 التطويرات المستقبلية

### المرحلة القادمة
- [ ] تصدير التقارير إلى PDF/Excel
- [ ] ربط مع أجهزة البصمة
- [ ] نظام الإجازات والعطل
- [ ] تقييم الأداء
- [ ] نظام الحوافز والمكافآت

### الميزات المتقدمة
- [ ] لوحة تحكم تحليلية
- [ ] تقارير مرئية بالرسوم البيانية
- [ ] نظام الإشعارات
- [ ] تكامل مع البريد الإلكتروني
- [ ] نظام الصلاحيات المتقدم

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- تحقق من ملفات السجلات (logs)
- راجع قاعدة البيانات للتأكد من سلامة الجداول
- تأكد من وجود جميع المكتبات المطلوبة

---

**تم تطوير هذه الوحدة بواسطة Augment Agent لتكون جزءاً متكاملاً من برنامج ست الكل للمحاسبة** 🚀
