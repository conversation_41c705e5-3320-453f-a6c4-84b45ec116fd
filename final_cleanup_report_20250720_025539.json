{"timestamp": "2025-07-20T02:55:39.726746", "files_fixed": 106, "patterns_fixed": ["duplicate_if_patterns", "indentation_issues", "incomplete_blocks"], "fixed_files_list": ["ui\\advanced_settings_window.py", "ui\\comprehensive_income_window.py", "ui\\comprehensive_sales_window.py", "ui\\enhanced_pos_window.py", "ui\\invoices_main_window.py", "ui\\pos_simple.py", "ui\\pos_window.py", "ui\\purchases_window.py", "ui\\sales_analysis_window.py", "ui\\structured_profit_loss_window.py", "ui\\treasury_window.py", "ui\\warehouses_management_window.py", "advanced_error_analyzer.py", "advanced_error_fixer.py", "advanced_syntax_fixer.py", "cleanup_unnecessary_files.py", "comma_fixer.py", "comprehensive_import_fixer.py", "comprehensive_income_formula_demo.py", "comprehensive_syntax_fixer.py", "comprehensive_system_checker.py", "critical_file_fixer.py", "database_analyzer.py", "deep_import_fixer.py", "duplicate_import_cleaner.py", "example_code.py", "except_block_fixer.py", "final_cleanup_tool.py", "fix_remaining_imports.py", "local_sqlserver_config.py", "main.py", "performance_optimizer.py", "precise_syntax_fixer.py", "run_app.py", "run_fixed_app.py", "safe_start.py", "setup.py", "start_app.py", "start_with_scheduler.py", "syntax_error_fixer.py", "test_hr_window.py", "test_settings.py", "ultimate_system_fixer.py", "auth\\auth_manager.py", "config\\arabic_fonts.py", "config\\postgresql_config.py", "config\\scheduler_settings.py", "config\\settings.py", "config\\sqlserver_config.py", "core\\app_core.py", "core\\barcode_scanner.py", "core\\error_handler.py", "core\\scheduler_manager.py", "database\\accounts_manager.py", "database\\accounts_tree_manager.py", "database\\comprehensive_income_manager.py", "database\\database_manager.py", "database\\fix_database.py", "database\\hybrid_database_manager.py", "database\\invoices_database_manager.py", "database\\invoices_manager.py", "database\\journal_entries_manager.py", "database\\postgresql_manager.py", "database\\products_manager.py", "database\\profit_loss_structure_manager.py", "database\\reports_manager.py", "database\\sqlserver_manager.py", "database\\warehouse_manager.py", "models\\customer.py", "models\\invoice.py", "models\\product.py", "reports\\report_generator.py", "services\\employees_manager.py", "services\\invoice_printer.py", "services\\postgresql_sales_manager.py", "services\\purchases_manager.py", "services\\sales_manager.py", "services\\treasury_manager.py", "themes\\font_manager.py", "themes\\modern_theme.py", "themes\\theme_manager.py", "ui\\accounts_tree_window.py", "ui\\accounts_window.py", "ui\\add_items_window.py", "ui\\advanced_financial_reports_window.py", "ui\\categories_management_window.py", "ui\\daily_journal_window.py", "ui\\employees_window.py", "ui\\employees_window_fixed.py", "ui\\hr_management_window.py", "ui\\inventory_window.py", "ui\\invoices_reports_window.py", "ui\\journal_entries_window.py", "ui\\login_window.py", "ui\\main_window.py", "ui\\pos_system_window.py", "ui\\reports_window.py", "ui\\sales_invoice_window.py", "ui\\sales_manager.py", "ui\\sales_window.py", "ui\\simple_welcome_window.py", "ui\\stock_management_window.py", "ui\\units_management_window.py", "ui\\user_management.py", "ui\\welcome_window.py", "ui\\window_utils.py"]}