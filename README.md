# برنامج ست الكل للمحاسبة 🏪

نظام إدارة المبيعات والمحاسبة المتكامل باللغة العربية مع دعم RTL كامل

## 📋 نظرة عامة

برنامج ست الكل للمحاسبة هو نظام محاسبي شامل مصمم خصيصاً للشركات والمتاجر العربية. يوفر البرنامج واجهة مستخدم عربية بالكامل مع دعم RTL وميزات محاسبية متقدمة، بالإضافة إلى دعم قواعد بيانات متعددة (SQLite & PostgreSQL) مع إمكانية التبديل التلقائي.

## ✨ الميزات الرئيسية

### �️ دعم قواعد البيانات المتعددة
- **SQLite** - قاعدة بيانات محلية سريعة (افتراضي)
- **PostgreSQL** - قاعدة بيانات متقدمة للمؤسسات
- **اكتشاف تلقائي** لنوع قاعدة البيانات المتاحة
- **ترحيل آمن** للبيانات بين الأنظمة
- **مدير مدمج ذكي** للتبديل بين الأنظمة

### �🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن مع تشفير كلمات المرور
- ثلاثة مستويات من الصلاحيات (مدير، محاسب، مستخدم)
- حماية من محاولات الدخول المتكررة
- تسجيل جميع أنشطة المستخدمين

### 📊 إدارة المبيعات
- إنشاء وإدارة فواتير المبيعات
- نظام ترقيم تلقائي للفواتير
- حساب الضرائب والخصومات
- تتبع حالة الدفع
- طباعة الفواتير بتنسيق احترافي

### 🛒 إدارة المشتريات
- تسجيل فواتير المشتريات
- ربط المشتريات بالموردين
- تحديث المخزون تلقائياً
- تتبع تكاليف البضائع

### 📦 إدارة المخزون
- تتبع المخزون في الوقت الفعلي
- تنبيهات نقص المخزون
- تسوية المخزون
- تحويل البضائع بين المخازن
- تقارير حركة المخزون

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء
- تتبع أرصدة العملاء
- حدود ائتمانية
- سجل المعاملات
- معلومات الاتصال والعناوين

### 💰 إدارة الخزينة
- تسجيل المقبوضات والمدفوعات
- تتبع رصيد الخزينة
- تصنيف المعاملات المالية
- تقارير التدفق النقدي

### 📈 التقارير والتحليل
- تقارير المبيعات اليومية والشهرية
- تقارير المخزون والحركة
- تقارير العملاء والموردين
- تحليل الأرباح والخسائر
- تصدير التقارير بصيغ PDF و Excel

### 🎨 واجهة المستخدم
- تصميم عربي بالكامل مع دعم RTL
- ثيمات متعددة (فاتح وداكن)
- واجهة سهلة الاستخدام
- أيقونات واضحة ومفهومة
- تخطيط شبكي منظم

### 🔧 ميزات تقنية
- قاعدة بيانات SQLite محلية
- نسخ احتياطي تلقائي
- استيراد وتصدير البيانات
- نظام سجلات شامل
- حماية البيانات

## 🛠️ متطلبات النظام

### الحد الأدنى
- نظام التشغيل: Windows 10 أو أحدث
- المعالج: Intel Core i3 أو معادل
- الذاكرة: 4 GB RAM
- مساحة القرص: 500 MB
- Python 3.8 أو أحدث

### الموصى به
- نظام التشغيل: Windows 11
- المعالج: Intel Core i5 أو أحدث
- الذاكرة: 8 GB RAM
- مساحة القرص: 2 GB
- Python 3.10 أو أحدث

## 📦 التثبيت

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/accounting-software.git
cd accounting-software
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
venv\Scripts\activate  # على Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تشغيل البرنامج
```bash
python main.py
```

## 🚀 الاستخدام السريع

### تسجيل الدخول الأول
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### الخطوات الأولى
1. **تغيير كلمة مرور المدير**
   - اذهب إلى الإعدادات → إدارة المستخدمين
   - غيّر كلمة مرور المدير الافتراضية

2. **إضافة المنتجات**
   - انقر على "إدخال الأصناف"
   - أضف منتجاتك مع الأسعار والمخزون

3. **إضافة العملاء**
   - انقر على "إدخال الحسابات"
   - أضف بيانات عملائك

4. **إنشاء أول فاتورة**
   - انقر على "بيع"
   - اختر العميل والمنتجات
   - احفظ الفاتورة

## 📁 هيكل المشروع

```
accounting-software/
├── main.py                 # نقطة البداية
├── requirements.txt        # المتطلبات
├── config/                 # ملفات الإعدادات
│   └── settings.py
├── core/                   # النواة الأساسية
│   └── app_core.py
├── database/               # إدارة قاعدة البيانات
│   └── database_manager.py
├── auth/                   # المصادقة والصلاحيات
│   └── auth_manager.py
├── models/                 # نماذج البيانات
│   ├── customer.py
│   ├── product.py
│   └── invoice.py
├── ui/                     # واجهات المستخدم
│   ├── main_window.py
│   ├── login_window.py
│   ├── user_management.py
│   └── backup_restore.py
├── themes/                 # الثيمات والتنسيق
│   └── theme_manager.py
├── reports/                # نظام التقارير
│   └── report_generator.py
├── assets/                 # الموارد (صور، أيقونات)
├── logs/                   # ملفات السجلات
└── backups/               # النسخ الاحتياطية
```

## 🔧 الإعدادات

### إعدادات قاعدة البيانات

#### SQLite (افتراضي)
```python
DB_SETTINGS = {
    "timeout": 30,
    "check_same_thread": False,
    "isolation_level": None
}
```

#### PostgreSQL (للمؤسسات)
```python
POSTGRESQL_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "database": "accounting_db",
    "user": "postgres",
    "password": "your_password"
}
```

**لتثبيت PostgreSQL:**
```bash
# تثبيت مكتبة Python
pip install psycopg2-binary

# راجع دليل التثبيت الكامل
docs/postgresql_installation_guide.md
```

### إعدادات الواجهة
```python
WINDOW_TITLE = "برنامج ست الكل للمحاسبة"
WINDOW_SIZE = "1920x1080"
RTL_SUPPORT = True
DEFAULT_FONT = "Cairo"
```

### إعدادات الأمان
```python
SECURITY = {
    "password_min_length": 6,
    "session_timeout": 3600,
    "max_login_attempts": 3
}
```

## 📊 لقطات الشاشة

### الشاشة الرئيسية
![الشاشة الرئيسية](screenshots/main_screen.png)

### نافذة تسجيل الدخول
![تسجيل الدخول](screenshots/login.png)

### إدارة المبيعات
![المبيعات](screenshots/sales.png)

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **الموقع الإلكتروني**: https://example.com
- **GitHub Issues**: [رفع مشكلة](https://github.com/your-username/accounting-software/issues)

## 🔄 التحديثات

### الإصدار 1.0.0 (الحالي)
- إطلاق النسخة الأولى
- جميع الميزات الأساسية
- واجهة عربية كاملة
- نظام تقارير متقدم

### خطط مستقبلية
- [ ] تطبيق ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع
- [ ] API للتكامل مع أنظمة أخرى
- [ ] تقارير ذكية بالذكاء الاصطناعي

## ⚠️ ملاحظات مهمة

1. **النسخ الاحتياطي**: تأكد من عمل نسخ احتياطية دورية
2. **الأمان**: غيّر كلمات المرور الافتراضية
3. **التحديثات**: تابع التحديثات الأمنية
4. **الدعم**: لا تتردد في طلب المساعدة

---

**تم تطوير هذا البرنامج بـ ❤️ للمجتمع العربي**
