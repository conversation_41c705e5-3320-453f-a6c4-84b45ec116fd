{"timestamp": "2025-07-20T02:48:27.186476", "total_files_processed": 42, "successfully_fixed": 0, "failed_to_fix": 42, "critical_errors": 4, "success_rate": 0.0, "fixed_files": [], "failed_files": ["ui\\main_window.py", "ui\\pos_window.py", "ui\\pos_simple.py", "ui\\sales_analysis_window.py", "ui\\accounts_tree_window.py", "ui\\accounts_window.py", "ui\\add_items_window.py", "ui\\advanced_financial_reports_window.py", "ui\\advanced_settings_window.py", "ui\\backup_restore.py", "ui\\categories_management_window.py", "ui\\comprehensive_income_window.py", "ui\\comprehensive_sales_window.py", "ui\\daily_journal_window.py", "ui\\employees_window.py", "ui\\employees_window_fixed.py", "ui\\enhanced_pos_window.py", "ui\\inventory_window.py", "ui\\invoices_main_window.py", "ui\\journal_entries_window.py", "ui\\login_window.py", "ui\\main_window.py", "ui\\pos_simple.py", "ui\\pos_window.py", "ui\\purchases_window.py", "ui\\reports_window.py", "ui\\sales_analysis_window.py", "ui\\sales_invoice_window.py", "ui\\sales_window.py", "ui\\simple_welcome_window.py", "ui\\stock_management_window.py", "ui\\structured_profit_loss_window.py", "ui\\treasury_window.py", "ui\\units_management_window.py", "ui\\user_management.py", "ui\\warehouses_management_window.py", "ui\\welcome_window.py", "database\\comprehensive_income_manager.py", "run_app.py", "safe_start.py", "start_with_scheduler.py", "comprehensive_income_formula_demo.py"], "critical_error_files": ["ui\\main_window.py", "ui\\pos_window.py", "ui\\pos_simple.py", "ui\\sales_analysis_window.py"]}