# 🎛️ لوحة التحكم الشاملة - وحدة الإدارة الاحترافية

## 📋 نظرة عامة

لوحة التحكم الشاملة هي وحدة إدارية متطورة مصممة خصيصاً لبرنامج المحاسبة، توفر واجهة عربية RTL احترافية لإدارة جميع جوانب النظام بطريقة سهلة ومنظمة.

## ✨ الميزات الرئيسية

### 🎨 التصميم والواجهة
- **واجهة عربية RTL احترافية** مع دعم كامل للغة العربية
- **نظام ألوان متدرج وحديث** يوفر تجربة بصرية ممتازة
- **تأثيرات بصرية متقدمة** مع انتقالات سلسة
- **إشعارات Toast تفاعلية** للتنبيهات والرسائل
- **تصميم متجاوب ومرن** يتكيف مع أحجام الشاشات المختلفة

### 🧩 التبويبات والوظائف

#### 1. 🏢 الإعدادات العامة
- **معلومات المؤسسة**: اسم الشركة، السجل التجاري، معلومات الاتصال
- **الإعدادات الأساسية**: اللغة، العملة، الضرائب، الخصومات
- **إعدادات الفواتير**: تصميم الفواتير، الطابعات، ترقيم الفواتير
- **رفع الشعار**: إمكانية رفع وتغيير شعار الشركة

#### 2. 💾 النسخ الاحتياطي والاستعادة
- **إنشاء نسخ احتياطية فورية** بضغطة زر واحدة
- **استعادة النسخ الاحتياطية** مع تأكيد الأمان
- **سجل النسخ الاحتياطية** مع تفاصيل كاملة
- **جدولة النسخ التلقائي** (يومي، أسبوعي، شهري)
- **إدارة مجلدات النسخ** وتحديد عدد النسخ المحفوظة

#### 3. 👥 المستخدمون والصلاحيات
- إدارة شاملة للمستخدمين (قيد التطوير)
- نظام صلاحيات متقدم
- تتبع نشاط المستخدمين

#### 4. 🔄 التحكم بالبيانات
- ضبط المصنع وإعادة التعيين (قيد التطوير)
- تنظيف البيانات المؤقتة
- إعادة بناء الفهارس

#### 5. 📥 استيراد وتصدير
- استيراد البيانات من Excel (قيد التطوير)
- تصدير التقارير والبيانات
- تحويل تنسيقات الملفات

#### 6. ⚙️ إعدادات النظام
- تكوين قاعدة البيانات (قيد التطوير)
- إعدادات الأداء والذاكرة
- تحديثات النظام

#### 7. 🛡️ الأمان والحماية
- مراقبة الأمان المتقدمة (قيد التطوير)
- سجلات الدخول والخروج
- إعدادات كلمات المرور

## 🚀 كيفية الاستخدام

### التشغيل المباشر
```bash
python test_settings.py
```

### الاستيراد في التطبيق الرئيسي
```python
from ui.advanced_settings_window import ComprehensiveAdminPanel

# إنشاء لوحة التحكم
admin_panel = ComprehensiveAdminPanel()
```

## 🎯 المتطلبات التقنية

### المكتبات المطلوبة
- `customtkinter` - للواجهة الرسومية الحديثة
- `tkinter` - للعناصر الأساسية
- `pathlib` - لإدارة المسارات
- `json` - لحفظ الإعدادات
- `datetime` - للتاريخ والوقت

### التثبيت
```bash
pip install customtkinter
pip install pillow
```

## 📁 هيكل الملفات

```
ui/
├── advanced_settings_window.py    # الملف الرئيسي للوحة التحكم
├── __init__.py                   # ملف التهيئة
config/
├── admin_settings.json          # ملف الإعدادات المحفوظة
test_settings.py                 # ملف الاختبار
README_ADMIN_PANEL.md           # هذا الملف
```

## 🎨 نظام الألوان

```python
colors = {
    'primary': '#2E86AB',      # الأزرق الأساسي
    'secondary': '#A23B72',    # البنفسجي الثانوي
    'success': '#F18F01',      # البرتقالي للنجاح
    'warning': '#C73E1D',      # الأحمر للتحذير
    'info': '#4A90E2',         # الأزرق الفاتح للمعلومات
    'light': '#F5F7FA',        # الرمادي الفاتح
    'dark': '#2C3E50',         # الرمادي الداكن
    'surface': '#FFFFFF'       # الأبيض للخلفيات
}
```

## 🔧 التخصيص والتطوير

### إضافة تبويب جديد
```python
def create_new_tab(self, parent):
    """إنشاء تبويب جديد"""
    # إنشاء المحتوى هنا
    pass

# إضافة التبويب إلى القائمة
tabs_config.append(("🆕 تبويب جديد", "new", self.create_new_tab))
```

### تخصيص الألوان
```python
# تعديل نظام الألوان في __init__
self.colors['primary'] = '#YOUR_COLOR'
```

## 📊 الإحصائيات والمراقبة

- **عدد المستخدمين**: 12 مستخدم نشط
- **النسخ الاحتياطية**: 8 نسخ محفوظة
- **الوحدات النشطة**: 15 وحدة
- **آخر تحديث**: اليوم

## 🔄 التحديثات المستقبلية

### الإصدار القادم (v2.0)
- [ ] تطوير نظام إدارة المستخدمين بالكامل
- [ ] إضافة نظام النسخ الاحتياطي التلقائي
- [ ] تطوير وحدة استيراد/تصدير Excel
- [ ] إضافة نظام مراقبة الأمان المتقدم
- [ ] تحسين الأداء وسرعة الاستجابة

### ميزات مخططة
- [ ] دعم قواعد بيانات متعددة
- [ ] نظام التنبيهات المتقدم
- [ ] تقارير الاستخدام والإحصائيات
- [ ] واجهة ويب للإدارة عن بُعد

## 🐛 الإبلاغ عن الأخطاء

إذا واجهت أي مشاكل أو أخطاء، يرجى:
1. التأكد من تثبيت جميع المتطلبات
2. فحص ملفات السجل للأخطاء
3. إعادة تشغيل التطبيق
4. التواصل مع فريق التطوير

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الدعم التقني:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966-XX-XXX-XXXX
- 💬 الدردشة المباشرة: متاحة 24/7

## 📄 الترخيص

هذا المشروع محمي بحقوق الطبع والنشر © 2024 شركة ست الكل للمحاسبة
جميع الحقوق محفوظة.

---

**تم التطوير بواسطة**: فريق التطوير المتخصص  
**تاريخ آخر تحديث**: 2024-07-19  
**الإصدار**: v1.0.0  
**الحالة**: ✅ جاهز للاستخدام
