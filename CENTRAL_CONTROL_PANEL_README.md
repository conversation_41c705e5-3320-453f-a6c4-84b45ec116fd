# 🎛️ لوحة التحكم المركزية الجذابة

## 📋 نظرة عامة
تم تصميم لوحة تحكم مركزية جذابة جداً لبرنامج المحاسبة العربي باستخدام Python وcustomtkinter، مع واجهة حديثة بألوان دافئة وجذابة، ودعم كامل للـ RTL.

---

## 🌟 المميزات الرئيسية

### ✨ التصميم الجذاب
- **ألوان دافئة وحديثة**: استخدام جميع الألوان الدافئة (مرجاني، برتقالي، ذهبي، نعناعي، لافندر، سماوي، وردي، خوخي)
- **منع اللون الرمادي**: تم تجنب استخدام اللون الرمادي كلياً كما طُلب
- **خلفية دافئة**: استخدام الأبيض الدافئ والكريمي الناعم
- **تأثيرات انتقالية**: تأثيرات ناعمة عند التمرير والنقر
- **خطوط عربية أنيقة**: دعم خطوط Cairo، Amiri، Noto Sans Arabic

### 🧭 التنقل السهل
- **شريط جانبي تفاعلي**: 11 قسم رئيسي مع أيقونات ملونة
- **أزرار جذابة**: كل زر بلون مميز ووصف واضح
- **تمييز القسم النشط**: تغيير لون الزر النشط
- **تمرير سلس**: منطقة قابلة للتمرير للمحتوى الطويل

---

## 🧩 الأقسام المتاحة

### 1. 🧩 الإعدادات العامة
- **معلومات الشركة**: الاسم، السجل التجاري، الهاتف، البريد، العنوان
- **الشعار**: تحميل شعار الشركة
- **الإعدادات الأساسية**: اللغة، تنسيق التاريخ، العملة، الوضع الليلي

### 2. 👥 المستخدمون والصلاحيات
- **إعدادات الأمان**: طول كلمة المرور، متطلبات الأرقام والرموز
- **إدارة الجلسات**: مدة انتهاء الجلسة، محاولات تسجيل الدخول

### 3. 🧾 إعدادات الفواتير
- **قوالب الفواتير**: حديث، كلاسيكي، مبسط
- **إعدادات الطباعة**: حجم الورق، عرض الشعار، الطباعة التلقائية
- **الضرائب**: معدل الضريبة الافتراضي

### 4. 💰 الرواتب والضرائب
- **ضرائب الدخل**: معدل ضريبة الدخل
- **التأمينات**: نسبة التأمينات الاجتماعية
- **ساعات العمل**: الساعات اليومية والأيام الأسبوعية

### 5. 🏪 إعدادات المخازن
- **تتبع المخزون**: تفعيل/إلغاء تتبع المخزون
- **تحذيرات المخزون**: تحذير نفاد المخزون، الحد الأدنى
- **الباركود**: استخدام نظام الباركود
- **طرق التقييم**: FIFO، LIFO، متوسط مرجح

### 6. 🔧 التحكم في الموديلات
- **الموديلات الأساسية**: المبيعات، المشتريات، المخازن، الحسابات، الرواتب
- **الموديلات المتقدمة**: POS، التقارير المتقدمة، CRM، الباركود، Excel

### 7. 💾 النسخ الاحتياطي
- **النسخ التلقائي**: تفعيل النسخ التلقائي، تحديد الفترة والوقت
- **إدارة النسخ**: إنشاء نسخة احتياطية، استعادة من نسخة
- **إعدادات متقدمة**: ضغط النسخ، عدد النسخ المحفوظة

### 8. 📊 استيراد من Excel
- **إعدادات الاستيراد**: السماح بالاستيراد، التحقق من البيانات
- **الأمان**: إنشاء نسخة احتياطية قبل الاستيراد
- **الحدود**: الحد الأقصى للصفوف المستوردة

### 9. 🎨 تخصيص الواجهة
- **الألوان**: اللون الأساسي، الخلفية، العناوين، الأزرار، النص
- **الخطوط**: الخط الأساسي، خط العناوين، حجم الخط
- **التنسيق**: خط عريض للعناوين

### 10. 🛡️ نظام الأمان
- **المراقبة**: تسجيل العمليات، مراقبة تسجيل الدخول
- **التشفير**: تشفير البيانات الحساسة
- **السجلات**: مدة حفظ السجلات
- **إجراءات خطيرة**: ضبط المصنع، عرض سجل العمليات

### 11. 🔢 الأرقام التسلسلية
- **ترقيم الفواتير**: بادئات فواتير البيع والشراء والمرتجعات
- **ترقيم الأشخاص**: بادئات الموظفين والعملاء والموردين
- **إعدادات الترقيم**: طول الرقم، تضمين السنة والشهر

---

## 🎨 نظام الألوان الدافئة

```python
WARM_COLORS = {
    'coral': '#FF6B6B',           # مرجاني دافئ
    'sunset': '#FF8E53',          # برتقالي غروب
    'golden': '#FFD93D',          # ذهبي مشرق
    'mint': '#6BCF7F',            # نعناعي منعش
    'lavender': '#A8E6CF',        # لافندر هادئ
    'sky': '#4ECDC4',             # سماوي صافي
    'rose': '#FF8A95',            # وردي ناعم
    'peach': '#FFAAA5',           # خوخي فاتح
    'turquoise': '#45B7D1',       # تركوازي
    'violet': '#96CEB4',          # بنفسجي فاتح
    'warm_white': '#FFFEF7',      # أبيض دافئ
    'soft_cream': '#FFF8E1',      # كريمي ناعم
}
```

---

## 🚀 كيفية الاستخدام

### 1. فتح لوحة التحكم
```python
# من النافذة الرئيسية - اضغط على أيقونة "الإعدادات" في الصف الأول
# أو برمجياً:
from ui.central_control_panel import open_central_control_panel
panel = open_central_control_panel(parent_window)
```

### 2. التنقل بين الأقسام
- استخدم الشريط الجانبي للانتقال بين الأقسام
- كل قسم يحتوي على بطاقات منظمة للإعدادات
- الألوان تساعد في التمييز بين الأقسام

### 3. تخصيص الإعدادات
- **الحقول النصية**: أدخل النص مباشرة
- **القوائم المنسدلة**: اختر من الخيارات المتاحة
- **مفاتيح التشغيل**: فعل/ألغ الخيارات
- **اختيار الألوان**: اضغط على "اختيار اللون"
- **اختيار الملفات**: اضغط على "اختيار ملف"

### 4. حفظ الإعدادات
- **💾 حفظ جميع الإعدادات**: حفظ دائم في ملف JSON
- **🔄 استعادة الافتراضي**: إعادة جميع الإعدادات للقيم الافتراضية
- **🎯 تجربة الإعدادات**: معاينة التغييرات قبل الحفظ

---

## 📁 هيكل الملفات

```
ui/
└── central_control_panel.py    # لوحة التحكم المركزية الجذابة

config/
└── control_panel_settings.json # ملف حفظ الإعدادات المخصصة

themes/
└── modern_theme.py             # الألوان والخطوط المستخدمة
```

---

## 🔧 الميزات التقنية

### ✅ الحفظ التلقائي
- حفظ جميع الإعدادات في ملف JSON
- تحميل الإعدادات المحفوظة عند فتح اللوحة
- استعادة الإعدادات الافتراضية

### ✅ معاينة الإعدادات
- نافذة معاينة تفاعلية
- عرض تأثير التغييرات قبل الحفظ
- إمكانية الحفظ المباشر من المعاينة

### ✅ إدارة النسخ الاحتياطي
- إنشاء نسخ احتياطية تلقائية
- استعادة من نسخ احتياطية
- حماية البيانات قبل العمليات الخطيرة

### ✅ الأمان المتقدم
- ضبط المصنع مع تأكيدات متعددة
- سجل العمليات والأنشطة
- حماية الإعدادات الحساسة

---

## 🎯 أمثلة على الأرقام التسلسلية

```
فواتير البيع: INV-2025-07-001
فواتير الشراء: PUR-2025-07-001
المرتجعات: RET-07-2025-003
الموظفين: EMP-HZ-00012
العملاء: CUS-2025-001
الموردين: SUP-2025-001
```

---

## 📞 الدعم والمساعدة

لأي استفسارات أو مشاكل تقنية، يرجى التواصل مع فريق الدعم الفني لبرنامج ست الكل للمحاسبة.

---

**تم التطوير بعناية فائقة لتوفير أفضل تجربة مستخدم عربية** 🇸🇦
