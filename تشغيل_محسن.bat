@echo off
chcp 65001 >nul
title برنامج ست الكل للمحاسبة - التشغيل المحسن

echo ======================================================================
echo 🎯 برنامج ست الكل للمحاسبة - التشغيل المحسن
echo ======================================================================
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 📋 اختر طريقة التشغيل:
echo 1. تشغيل سريع
echo 2. فحص شامل ثم تشغيل  
echo 3. إصلاح ثم تشغيل
echo 4. تشغيل الملف المصحح
echo.

set /p choice="اختر (1-4): "

if "%choice%"=="1" (
    echo 🚀 تشغيل سريع...
    python main.py
) else if "%choice%"=="2" (
    echo 🔍 فحص شامل...
    python فحص_وتشغيل_شامل.py
) else if "%choice%"=="3" (
    echo 🔧 إصلاح شامل...
    python إصلاح_شامل_للبرنامج.py
    echo 🚀 تشغيل البرنامج...
    python main.py
) else if "%choice%"=="4" (
    echo 🛠️ تشغيل النسخة المصححة...
    python تشغيل_البرنامج_المصحح.py
) else (
    echo ❌ اختيار غير صحيح
    pause
    exit /b 1
)

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج
    echo 💡 جرب الخيارات التالية:
    echo    - تشغيل الفحص الشامل
    echo    - تشغيل الإصلاح الشامل
    echo    - التحقق من ملفات السجل في مجلد logs
    echo.
    pause
)

echo.
echo 👋 شكراً لاستخدام البرنامج
pause