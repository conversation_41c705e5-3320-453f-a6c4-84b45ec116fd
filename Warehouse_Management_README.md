# 🏪 نافذة إدارة المخازن والمستودعات الاحترافية

## 📋 نظرة عامة

تم تطوير واجهة احترافية متكاملة وجذابة بصريًا لإدارة وحدة المخازن والمستودعات داخل برنامج محاسبة عربي باستخدام Python وcustomtkinter. التصميم مستوحى من أنظمة ERP الحديثة مثل Odoo وZoho مع دعم كامل للغة العربية RTL.

## 🎨 التصميم والواجهة

### نظام الألوان الاحترافي
```css
/* ألوان أساسية للواجهة */
background_main: #F5F7FA     /* خلفية ناعمة */
sidebar_bg: #2C3E50          /* شريط جانبي داكن */
main_content: #FFFFFF        /* المحتوى الرئيسي */

/* ألوان العمليات */
add_operation: #27AE60       /* أخضر لإذن الإضافة */
out_operation: #E74C3C       /* أحمر لإذن الصرف */
transfer_operation: #3498DB  /* أزرق للتحويلات */
report_operation: #9B59B6    /* بنفسجي للتقارير */
```

### تخطيط ERP حديث
- **شريط جانبي (Sidebar)**: قائمة تنقل بأيقونات تعبيرية
- **منطقة المحتوى الرئيسي**: عرض ديناميكي للمحتوى
- **لوحة تحكم**: إحصائيات وعمليات سريعة
- **بطاقات تفاعلية**: عرض البيانات بشكل جذاب

## 🔧 الميزات الرئيسية

### 1. 📊 **لوحة التحكم الذكية:**
- **إحصائيات سريعة**: عدد المخازن، الأصناف، قيمة المخزون
- **تنبيهات ذكية**: أصناف تحت الحد الأدنى، أصناف راكدة
- **عمليات سريعة**: أزرار مباشرة للعمليات الشائعة
- **بطاقات ملونة**: كل نوع عملية له لون مميز

### 2. 🔄 العمليات اليومية
#### إذن الإضافة (📦)
- **رقم تسلسلي تلقائي**: `ADD-2025-07-001`
- **اختيار المخزن**: قائمة منسدلة بالمخازن النشطة
- **إضافة أصناف**: جدول تفاعلي للأصناف
- **حفظ وطباعة**: أزرار احترافية للحفظ والطباعة

#### إذن الصرف (📤)
- **رقم تسلسلي تلقائي**: `OUT-2025-07-002`
- **تحقق من الرصيد**: منع الصرف بكمية أكبر من المتاح
- **تتبع التكلفة**: حساب تكلفة الصرف

#### التحويلات (🔄)
- **تحويل بين المخازن**: `TRF-2025-07-003`
- **تحويل بين الفروع**: إدارة متقدمة للفروع
- **تتبع الحالة**: معلق، مكتمل، ملغي

### 3. 📊 التقارير والتحليلات
- **رصيد المخزن الحالي**: كمية فقط أو بالأسعار
- **حركة الأصناف**: خلال فترة محددة
- **أرباح الأصناف**: تحليل الربحية
- **تقرير المبيعات**: إجمالي مبيعات الأصناف
- **جرد المخازن**: يدوي وذكي

### 4. 🛠 أدوات الإدارة
- **طباعة الباركود**: مولد باركود احترافي
- **تنبيهات ذكية**: أصناف تحت حد الطلب
- **كشف الأصناف الراكدة**: تحليل الحركة
- **إدارة المخازن**: إضافة وتعديل المخازن

### 2. 🔄 العمليات اليومية
#### إذن الإضافة (📦)
- **رقم تسلسلي تلقائي**: `ADD-2025-07-001`
- **اختيار المخزن**: قائمة منسدلة بالمخازن النشطة
- **إضافة أصناف**: جدول تفاعلي للأصناف
- **حفظ وطباعة**: أزرار احترافية للحفظ والطباعة

#### إذن الصرف (📤)
- **رقم تسلسلي تلقائي**: `OUT-2025-07-002`
- **تحقق من الرصيد**: منع الصرف بكمية أكبر من المتاح
- **تتبع التكلفة**: حساب تكلفة الصرف

#### التحويلات (🔄)
- **تحويل بين المخازن**: `TRF-2025-07-003`
- **تحويل بين الفروع**: إدارة متقدمة للفروع
- **تتبع الحالة**: معلق، مكتمل، ملغي

### 3. 📊 التقارير والتحليلات
- **رصيد المخزن الحالي**: كمية فقط أو بالأسعار
- **حركة الأصناف**: خلال فترة محددة
- **أرباح الأصناف**: تحليل الربحية
- **تقرير المبيعات**: إجمالي مبيعات الأصناف
- **جرد المخازن**: يدوي وذكي

### 4. 🛠 أدوات الإدارة
- **طباعة الباركود**: مولد باركود احترافي
- **تنبيهات ذكية**: أصناف تحت حد الطلب
- **كشف الأصناف الراكدة**: تحليل الحركة
- **إدارة المخازن**: إضافة وتعديل المخازن

## 🔐 نظام الأرقام التسلسلية التلقائية

### آلية التوليد الذكية
```python
def generate_serial_number(self, movement_type):
    """توليد رقم تسلسلي تلقائي"""
    prefixes = {
        'add': 'ADD',        # إذن إضافة
        'out': 'OUT',        # إذن صرف  
        'transfer': 'TRF',   # تحويل
        'adjustment': 'ADJ'  # تسوية
    }
    
    # نمط: PREFIX-YYYY-MM-XXX
    # مثال: ADD-2025-07-001
```

### خصائص الأرقام التسلسلية
- **فريدة**: لا يمكن تكرارها
- **ذكية**: تعتمد على النوع والتاريخ
- **متسلسلة**: ترقيم تلقائي متتالي
- **منظمة**: سهولة البحث والفرز

## 🗄️ هيكل قاعدة البيانات

### جدول المخازن (warehouses)
```sql
CREATE TABLE warehouses (
    id INTEGER PRIMARY KEY,
    warehouse_code TEXT UNIQUE,
    name TEXT NOT NULL,
    location TEXT,
    manager_name TEXT,
    capacity DECIMAL(10,2),
    current_stock DECIMAL(10,2),
    status TEXT DEFAULT 'active'
);
```

### جدول الأصناف (items)
```sql
CREATE TABLE items (
    id INTEGER PRIMARY KEY,
    item_code TEXT UNIQUE,
    name TEXT NOT NULL,
    category TEXT,
    unit TEXT,
    cost_price DECIMAL(10,2),
    sale_price DECIMAL(10,2),
    min_stock DECIMAL(10,2),
    max_stock DECIMAL(10,2),
    barcode TEXT
);
```

### جدول حركات المخزن (warehouse_movements)
```sql
CREATE TABLE warehouse_movements (
    id INTEGER PRIMARY KEY,
    serial_number TEXT UNIQUE,
    movement_type TEXT NOT NULL,
    warehouse_id INTEGER,
    item_id INTEGER,
    quantity DECIMAL(10,2),
    unit_price DECIMAL(10,2),
    total_value DECIMAL(10,2),
    movement_date DATE,
    notes TEXT
);
```

### جدول أرصدة المخزن (warehouse_stock)
```sql
CREATE TABLE warehouse_stock (
    id INTEGER PRIMARY KEY,
    warehouse_id INTEGER,
    item_id INTEGER,
    quantity DECIMAL(10,2),
    last_cost DECIMAL(10,2),
    total_value DECIMAL(10,2),
    last_updated TIMESTAMP
);
```

### جدول التحويلات (warehouse_transfers)
```sql
CREATE TABLE warehouse_transfers (
    id INTEGER PRIMARY KEY,
    serial_number TEXT UNIQUE,
    from_warehouse_id INTEGER,
    to_warehouse_id INTEGER,
    item_id INTEGER,
    quantity DECIMAL(10,2),
    transfer_date DATE,
    status TEXT DEFAULT 'pending'
);
```

## 🎯 قائمة التنقل الجانبية

### العمليات الأساسية
- 📊 **لوحة التحكم**: نظرة شاملة على النظام
- 📦 **إذن إضافة**: إضافة أصناف للمخزن
- 📤 **إذن صرف**: صرف أصناف من المخزن
- 🔄 **تحويل بين المخازن**: نقل الأصناف
- 🏢 **تحويل بين الفروع**: إدارة الفروع

### التقارير والاستعلامات
- � **رصيد المخزن**: عرض الأرصدة الحالية
- 📈 **حركة الأصناف**: تتبع الحركات
- 💰 **أرباح الأصناف**: تحليل الربحية
- 📊 **تقرير المبيعات**: إحصائيات المبيعات
- � **جرد المخازن**: عمليات الجرد

### الأدوات المساعدة
- 🏷️ **طباعة الباركود**: مولد الباركود
- ⚠️ **تنبيهات المخزن**: التنبيهات الذكية

## 🚀 كيفية الاستخدام

### 1. الوصول للنافذة
```python
# من الشريط العلوي
اضغط على "المخازن"

# من الأيقونات الخضراء (الصف الثالث)
اضغط على "🏬 إدارة المخازن"

# من إعدادات النظام
اضغط على "🏪 إدارة المخازن"
```

### 2. إنشاء إذن إضافة
1. اضغط على "📦 إذن إضافة" من الشريط الجانبي
2. سيتم توليد رقم تسلسلي تلقائياً
3. اختر المخزن من القائمة المنسدلة
4. أضف الأصناف مع الكميات والأسعار
5. اضغط "💾 حفظ الإذن"

### 3. عرض التقارير
1. اضغط على التقرير المطلوب من الشريط الجانبي
2. حدد الفترة الزمنية (إن وجدت)
3. اضغط "عرض" أو "تصدير"

## 🎨 التخصيص والتطوير

### إضافة عملية جديدة
```python
def show_new_operation(self):
    """عرض عملية جديدة"""
    # مسح المحتوى الحالي
    for widget in self.main_content_frame.winfo_children():
        widget.destroy()
    
    # إنشاء المحتوى الجديد
    # ...
```

### إضافة عنصر قائمة جديد
```python
# في create_navigation_menu
menu_items.append(
    ("🆕", "عملية جديدة", "new_operation", WAREHOUSE_COLORS['accent_blue'])
)
```

## 📱 التوافق والمتطلبات

### المكتبات المطلوبة
```python
customtkinter>=5.0.0
tkinter (مدمجة مع Python)
PIL (Pillow)>=8.0.0
pandas>=1.3.0
sqlite3 (مدمجة مع Python)
```

### متطلبات النظام
- **Python**: 3.8 أو أحدث
- **نظام التشغيل**: Windows, macOS, Linux
- **الذاكرة**: 4GB RAM (مستحسن)
- **المساحة**: 100MB مساحة فارغة

## 🔮 التطويرات المستقبلية

### المرحلة القادمة
- [ ] تصدير التقارير إلى PDF/Excel
- [ ] ربط مع قارئ الباركود
- [ ] نظام الموافقات للتحويلات
- [ ] تقارير مرئية بالرسوم البيانية
- [ ] نظام التنبيهات المتقدم

### الميزات المتقدمة
- [ ] تكامل مع أنظمة ERP خارجية
- [ ] API للتكامل مع التطبيقات الأخرى
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] تتبع GPS للمخازن
- [ ] نظام إدارة الصلاحيات المتقدم

---

**تم تطوير هذه الواجهة بواسطة Augment Agent لتكون جزءاً متكاملاً من برنامج ست الكل للمحاسبة** 🚀
