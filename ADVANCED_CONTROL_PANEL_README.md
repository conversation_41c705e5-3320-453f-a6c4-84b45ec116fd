# 🎛️ لوحة التحكم المركزية المطورة

## 📋 نظرة عامة

لوحة التحكم المركزية المطورة هي واجهة شاملة ومتقدمة لإدارة جميع إعدادات نظام المحاسبة. تم تطويرها بعناية فائقة لتوفر تحكماً كاملاً في جميع جوانب النظام مع واجهة مستخدم عربية جذابة وسهلة الاستخدام.

## ✨ الميزات الرئيسية

### 🧾 1. إعدادات الفواتير المتقدمة
- **قوالب متعددة**: حديث، كلاسيكي، مبسط، احترافي، ملون، مخصص
- **إعدادات الطباعة**: أحجام ورق متنوعة، جودة طباعة قابلة للتخصيص
- **الضرائب والخصومات**: نظام ضرائب متقدم مع خصومات ذكية
- **إعدادات POS**: تكامل كامل مع أنظمة نقاط البيع

### 💰 2. نظام الرواتب والضرائب الشامل
- **سلالم الرواتب**: إدارة متقدمة لسلالم الرواتب حسب الأقسام
- **حاسبة الرواتب**: حساب تلقائي للرواتب مع البدلات والخصومات
- **شرائح الضرائب**: نظام ضريبي تصاعدي قابل للتخصيص
- **إعدادات الإضافي**: حساب ساعات الإضافي بأنواعها المختلفة

### 🏪 3. إدارة المخازن المتطورة
- **إدارة المخازن**: إضافة وتعديل المخازن مع تتبع المواقع
- **أذون الحركة**: نظام شامل لأذون الصرف والإضافة والتحويل
- **نظام الجرد**: جرد دوري ومستمر مع تنبيهات ذكية
- **تكامل الباركود**: قراءة وطباعة الباركود مع أنواع متعددة

### 👥 4. إدارة المستخدمين والصلاحيات
- **إدارة المستخدمين**: إضافة وتعديل وحذف المستخدمين
- **الأدوار والصلاحيات**: نظام أدوار متقدم (مدير، محاسب، مستخدم)
- **إعدادات الأمان**: كلمات مرور قوية وجلسات آمنة
- **تخصيص الصلاحيات**: تحكم دقيق في صلاحيات كل دور

### 🔧 5. التحكم في الموديلات
- **الموديلات الأساسية**: المبيعات، المشتريات، المخازن، الحسابات، الرواتب
- **الموديلات المتقدمة**: POS، التقارير، CRM، الباركود، Excel
- **الموديلات الإضافية**: التصنيع، المشاريع، الموارد البشرية، الأصول، الجودة

### 💾 6. نظام النسخ الاحتياطي المتقدم
- **الجدولة التلقائية**: نسخ تلقائية بتكرارات مختلفة
- **إدارة النسخ**: عرض وإدارة النسخ الاحتياطية المتاحة
- **الاستعادة**: استعادة سريعة من أي نسخة احتياطية
- **الضغط والتشفير**: خيارات متقدمة لحماية النسخ

### 📊 7. استيراد وتصدير البيانات
- **أنواع البيانات المدعومة**: المنتجات، العملاء، الفواتير، المخزون
- **التحقق من البيانات**: فحص تلقائي لصحة البيانات المستوردة
- **النسخ الاحتياطي**: إنشاء نسخة احتياطية قبل الاستيراد
- **معالجة الأخطاء**: تقارير مفصلة عن أي أخطاء في البيانات

### 🎨 8. تخصيص الواجهة والثيمات
- **الثيمات الجاهزة**: الدافئ، الطبيعي، الملكي، المخصص
- **منتقي الألوان**: اختيار ألوان مخصصة للواجهة
- **الخطوط العربية**: دعم كامل للخطوط العربية الجميلة
- **أحجام الخطوط**: تخصيص أحجام الخطوط حسب الحاجة

### 🛡️ 9. نظام الأمان المتطور
- **مستويات الأمان**: أساسي، متوسط، عالي، عسكري
- **التشفير**: تشفير البيانات الحساسة
- **سجل العمليات**: تتبع جميع العمليات والتغييرات
- **الإقفال الجزئي**: حماية أجزاء معينة من النظام

### 🔢 10. الأرقام التسلسلية القابلة للتخصيص
- **قوالب الترقيم**: قوالب مرنة لجميع أنواع الفواتير
- **التسلسل التلقائي**: توليد أرقام تلقائية بأنماط مختلفة
- **التخصيص الكامل**: إمكانية تخصيص كامل لأنماط الترقيم
- **أمثلة حية**: عرض أمثلة فورية للأرقام المولدة

### ⚙️ 11. إعدادات النظام الشاملة
- **معلومات الشركة**: بيانات كاملة عن الشركة
- **إعدادات اللغة**: دعم متعدد اللغات
- **إعدادات العملة**: تخصيص العملات والأسعار
- **إعدادات الطباعة**: تحكم كامل في خيارات الطباعة

## 🚀 التشغيل السريع

### المتطلبات
```bash
pip install customtkinter
```

### التشغيل
```bash
python test_advanced_control_panel.py
```

## 📁 هيكل الملفات

```
├── ui/
│   ├── central_control_panel.py          # لوحة التحكم الرئيسية
│   ├── advanced_sections.py              # الأقسام المتقدمة (الجزء الأول)
│   ├── advanced_sections_part2.py        # الأقسام المتقدمة (الجزء الثاني)
│   └── control_panel_integration.py      # تكامل الأقسام
├── themes/
│   └── modern_theme.py                   # الثيمات والألوان
├── config/
│   └── settings.py                       # إعدادات المشروع
├── data/
│   └── settings/                         # ملفات الإعدادات المحفوظة
├── assets/
│   └── icons/                           # الأيقونات والصور
├── test_advanced_control_panel.py       # ملف التشغيل والاختبار
└── ADVANCED_CONTROL_PANEL_README.md     # هذا الملف
```

## 🎨 الألوان والثيمات

تستخدم لوحة التحكم مجموعة ألوان دافئة وجذابة:

- **🔴 المرجاني**: `#FF6B6B` - للعناصر الرئيسية
- **🟠 البرتقالي**: `#FF8E53` - للتنبيهات والإشعارات
- **🟡 الذهبي**: `#FFD93D` - للعناصر المميزة
- **🟢 النعناعي**: `#6BCF7F` - للحالات الإيجابية
- **🔵 السماوي**: `#4ECDC4` - للعناصر التفاعلية
- **🟣 البنفسجي**: `#96CEB4` - للعناصر الثانوية

## 🔧 التخصيص والتطوير

### إضافة قسم جديد
1. إنشاء كلاس جديد في `advanced_sections.py`
2. تنفيذ دالة `create_[section_name]_settings`
3. إضافة التكامل في `control_panel_integration.py`
4. تحديث قائمة الأقسام في لوحة التحكم الرئيسية

### تخصيص الألوان
```python
WARM_COLORS = {
    'custom_color': '#YOUR_HEX_COLOR',
    # إضافة ألوان جديدة
}
```

### إضافة ثيم جديد
```python
new_theme = {
    "name": "الثيم الجديد",
    "colors": ["#COLOR1", "#COLOR2", "#COLOR3"],
    "description": "وصف الثيم"
}
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في الاستيراد**
   - تأكد من تثبيت `customtkinter`
   - تحقق من وجود جميع الملفات المطلوبة

2. **مشاكل في الواجهة**
   - تأكد من دعم النظام للخطوط العربية
   - تحقق من إعدادات العرض

3. **مشاكل في الحفظ**
   - تأكد من صلاحيات الكتابة في مجلد البيانات
   - تحقق من مساحة القرص المتاحة

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 💬 الدردشة المباشرة: متاحة في النظام
- 📚 الوثائق: دليل المستخدم الشامل

## 🎉 الخلاصة

لوحة التحكم المركزية المطورة تمثل قمة التطور في إدارة أنظمة المحاسبة. بواجهتها العربية الجذابة وميزاتها المتقدمة، توفر تجربة استخدام استثنائية وتحكماً كاملاً في جميع جوانب النظام.

**استمتع بالتحكم الكامل في نظامك! 🚀**
