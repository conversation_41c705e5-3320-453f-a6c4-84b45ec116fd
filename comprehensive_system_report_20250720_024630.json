{"timestamp": "2025-07-20T02:46:29.546533", "project_structure": {"ui": {"exists": true, "files_count": 39, "files": ["accounts_tree_window.py", "accounts_window.py", "add_items_window.py", "advanced_financial_reports_window.py", "advanced_settings_window.py", "backup_restore.py", "categories_management_window.py", "comprehensive_income_window.py", "comprehensive_sales_window.py", "daily_journal_window.py", "employees_window.py", "employees_window_fixed.py", "enhanced_pos_window.py", "hr_management_window.py", "inventory_window.py", "invoices_main_window.py", "invoices_reports_window.py", "journal_entries_window.py", "login_window.py", "main_window.py", "pos_simple.py", "pos_system_window.py", "pos_window.py", "purchases_window.py", "reports_window.py", "sales_analysis_window.py", "sales_invoice_window.py", "sales_manager.py", "sales_window.py", "simple_welcome_window.py", "stock_management_window.py", "structured_profit_loss_window.py", "treasury_window.py", "units_management_window.py", "user_management.py", "warehouses_management_window.py", "welcome_window.py", "window_utils.py", "__init__.py"]}, "database": {"exists": true, "files_count": 16, "files": ["accounts_manager.py", "accounts_tree_manager.py", "comprehensive_income_manager.py", "database_manager.py", "fix_database.py", "hybrid_database_manager.py", "invoices_database_manager.py", "invoices_manager.py", "journal_entries_manager.py", "postgresql_manager.py", "products_manager.py", "profit_loss_structure_manager.py", "reports_manager.py", "sqlserver_manager.py", "warehouse_manager.py", "__init__.py"]}, "services": {"exists": true, "files_count": 7, "files": ["employees_manager.py", "invoice_printer.py", "postgresql_sales_manager.py", "purchases_manager.py", "sales_manager.py", "treasury_manager.py", "__init__.py"]}, "core": {"exists": true, "files_count": 5, "files": ["app_core.py", "barcode_scanner.py", "error_handler.py", "scheduler_manager.py", "__init__.py"]}, "themes": {"exists": true, "files_count": 4, "files": ["font_manager.py", "modern_theme.py", "theme_manager.py", "__init__.py"]}, "auth": {"exists": true, "files_count": 2, "files": ["auth_manager.py", "__init__.py"]}, "config": {"exists": true, "files_count": 6, "files": ["arabic_fonts.py", "postgresql_config.py", "scheduler_settings.py", "settings.py", "sqlserver_config.py", "__init__.py"]}}, "syntax_errors": [{"file": "comprehensive_income_formula_demo.py", "line": 254, "column": 8, "error": "unexpected indent (<unknown>, line 254)", "severity": "medium"}, {"file": "run_app.py", "line": 43, "column": 8, "error": "unexpected indent (<unknown>, line 43)", "severity": "medium"}, {"file": "run_fixed_app.py", "line": 155, "column": 8, "error": "unexpected indent (<unknown>, line 155)", "severity": "medium"}, {"file": "safe_start.py", "line": 42, "column": 8, "error": "unexpected indent (<unknown>, line 42)", "severity": "medium"}, {"file": "start_with_scheduler.py", "line": 81, "column": 8, "error": "unexpected indent (<unknown>, line 81)", "severity": "medium"}, {"file": "config\\postgresql_config.py", "line": 151, "column": 12, "error": "unexpected indent (<unknown>, line 151)", "severity": "medium"}, {"file": "core\\app_core.py", "line": 299, "column": 12, "error": "unexpected indent (<unknown>, line 299)", "severity": "medium"}, {"file": "database\\comprehensive_income_manager.py", "line": 274, "column": 79, "error": "invalid syntax (<unknown>, line 274)", "severity": "medium"}, {"file": "ui\\accounts_tree_window.py", "line": 513, "column": 14, "error": "invalid syntax (<unknown>, line 513)", "severity": "medium"}, {"file": "ui\\accounts_window.py", "line": 422, "column": 14, "error": "invalid syntax (<unknown>, line 422)", "severity": "medium"}, {"file": "ui\\add_items_window.py", "line": 1797, "column": 18, "error": "invalid syntax (<unknown>, line 1797)", "severity": "medium"}, {"file": "ui\\advanced_financial_reports_window.py", "line": 1290, "column": 14, "error": "invalid syntax (<unknown>, line 1290)", "severity": "medium"}, {"file": "ui\\advanced_settings_window.py", "line": 515, "column": 18, "error": "invalid syntax (<unknown>, line 515)", "severity": "medium"}, {"file": "ui\\backup_restore.py", "line": 710, "column": 8, "error": "unexpected indent (<unknown>, line 710)", "severity": "medium"}, {"file": "ui\\categories_management_window.py", "line": 729, "column": 14, "error": "invalid syntax (<unknown>, line 729)", "severity": "medium"}, {"file": "ui\\comprehensive_income_window.py", "line": 477, "column": 4, "error": "expected an indented block after 'if' statement on line 476 (<unknown>, line 477)", "severity": "medium"}, {"file": "ui\\comprehensive_sales_window.py", "line": 368, "column": 4, "error": "expected an indented block after 'if' statement on line 367 (<unknown>, line 368)", "severity": "medium"}, {"file": "ui\\daily_journal_window.py", "line": 685, "column": 9, "error": "expected an indented block after 'finally' statement on line 684 (<unknown>, line 685)", "severity": "medium"}, {"file": "ui\\employees_window.py", "line": 872, "column": 22, "error": "invalid syntax (<unknown>, line 872)", "severity": "medium"}, {"file": "ui\\employees_window_fixed.py", "line": 608, "column": 22, "error": "invalid syntax (<unknown>, line 608)", "severity": "medium"}, {"file": "ui\\enhanced_pos_window.py", "line": 715, "column": 4, "error": "expected an indented block after 'if' statement on line 714 (<unknown>, line 715)", "severity": "medium"}, {"file": "ui\\inventory_window.py", "line": 675, "column": 14, "error": "invalid syntax (<unknown>, line 675)", "severity": "medium"}, {"file": "ui\\invoices_main_window.py", "line": 257, "column": 4, "error": "expected an indented block after 'if' statement on line 256 (<unknown>, line 257)", "severity": "medium"}, {"file": "ui\\journal_entries_window.py", "line": 479, "column": 14, "error": "invalid syntax (<unknown>, line 479)", "severity": "medium"}, {"file": "ui\\login_window.py", "line": 244, "column": 18, "error": "invalid syntax (<unknown>, line 244)", "severity": "medium"}, {"file": "ui\\main_window.py", "line": 1680, "column": 4, "error": "expected an indented block after 'if' statement on line 1679 (<unknown>, line 1680)", "severity": "medium"}, {"file": "ui\\pos_simple.py", "line": 82, "column": 12, "error": "unexpected indent (<unknown>, line 82)", "severity": "medium"}, {"file": "ui\\pos_window.py", "line": 54, "column": 12, "error": "unexpected indent (<unknown>, line 54)", "severity": "medium"}, {"file": "ui\\purchases_window.py", "line": 460, "column": 4, "error": "expected an indented block after 'if' statement on line 459 (<unknown>, line 460)", "severity": "medium"}, {"file": "ui\\reports_window.py", "line": 526, "column": 14, "error": "invalid syntax (<unknown>, line 526)", "severity": "medium"}, {"file": "ui\\sales_analysis_window.py", "line": 394, "column": 18, "error": "invalid syntax (<unknown>, line 394)", "severity": "medium"}, {"file": "ui\\sales_invoice_window.py", "line": 406, "column": 18, "error": "invalid syntax (<unknown>, line 406)", "severity": "medium"}, {"file": "ui\\sales_window.py", "line": 520, "column": 14, "error": "invalid syntax (<unknown>, line 520)", "severity": "medium"}, {"file": "ui\\simple_welcome_window.py", "line": 326, "column": 34, "error": "invalid syntax (<unknown>, line 326)", "severity": "medium"}, {"file": "ui\\stock_management_window.py", "line": 1410, "column": 14, "error": "invalid syntax (<unknown>, line 1410)", "severity": "medium"}, {"file": "ui\\structured_profit_loss_window.py", "line": 527, "column": 4, "error": "expected an indented block after 'if' statement on line 526 (<unknown>, line 527)", "severity": "medium"}, {"file": "ui\\treasury_window.py", "line": 496, "column": 4, "error": "expected an indented block after 'if' statement on line 495 (<unknown>, line 496)", "severity": "medium"}, {"file": "ui\\units_management_window.py", "line": 593, "column": 14, "error": "invalid syntax (<unknown>, line 593)", "severity": "medium"}, {"file": "ui\\user_management.py", "line": 537, "column": 14, "error": "invalid syntax (<unknown>, line 537)", "severity": "medium"}, {"file": "ui\\warehouses_management_window.py", "line": 1180, "column": 4, "error": "expected an indented block after 'if' statement on line 1179 (<unknown>, line 1180)", "severity": "medium"}, {"file": "ui\\welcome_window.py", "line": 430, "column": 18, "error": "invalid syntax (<unknown>, line 430)", "severity": "medium"}], "import_errors": [{"file": "ui/main_window.py", "error": "نمط استيراد مشكوك فيه: except ImportError:\\s*pass", "severity": "medium"}, {"file": "ui/main_window.py", "error": "نمط استيراد مشكوك فيه: import\\s+\\w+\\s*\\n\\s*except", "severity": "medium"}, {"file": "database/hybrid_database_manager.py", "error": "نمط استيراد مشكوك فيه: except ImportError:\\s*pass", "severity": "medium"}], "database_issues": [{"issue": "جداول مفقودة: ['invoices']", "severity": "high"}], "ui_issues": [], "performance_issues": [], "security_issues": [], "recommendations": ["إصلاح الأخطاء النحوية فوراً", "فحص وإصلاح قاعدة البيانات", "مراجعة وتنظيم الاستيرادات", "إجراء نسخة احتياطية قبل أي تعديلات", "اختبار جميع الوحدات بعد الإصلاحات", "تحديث التوثيق والتعليقات"], "fixed_issues": [], "test_results": {"customtkinter": {"status": "success", "error": null}, "tkinter": {"status": "success", "error": null}, "sqlite3": {"status": "success", "error": null}, "pathlib": {"status": "success", "error": null}, "datetime": {"status": "success", "error": null}, "json": {"status": "success", "error": null}, "logging": {"status": "success", "error": null}, "traceback": {"status": "success", "error": null}, "main.py": {"status": "success", "error": null}, "ui/main_window.py": {"status": "failed", "error": "expected an indented block after 'if' statement on line 1679 (<unknown>, line 1680)"}, "database/hybrid_database_manager.py": {"status": "success", "error": null}, "services/sales_manager.py": {"status": "success", "error": null}, "core/scheduler_manager.py": {"status": "success", "error": null}, "themes/theme_manager.py": {"status": "success", "error": null}, "auth/auth_manager.py": {"status": "success", "error": null}}, "overall_status": "needs_attention"}