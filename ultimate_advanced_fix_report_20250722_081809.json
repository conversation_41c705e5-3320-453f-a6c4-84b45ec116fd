{"timestamp": "2025-07-22T08:18:09.999047", "total_files_processed": 24, "successfully_fixed": 0, "failed_to_fix": 24, "success_rate": 0.0, "fixed_files": [], "failed_files": ["advanced_error_analyzer.py", "advanced_error_fixer.py", "advanced_syntax_fixer.py", "comprehensive_income_formula_demo.py", "deep_comprehensive_fixer.py", "deep_import_fixer.py", "quick_pattern_fixer.py", "run_app.py", "run_fixed_app.py", "safe_start.py", "start_with_scheduler.py", "ultimate_system_fixer.py", "config\\postgresql_config.py", "core\\app_core.py", "database\\comprehensive_income_manager.py", "database\\fix_database.py", "ui\\daily_journal_window.py", "ui\\sales_analysis_window.py", "config\\postgresql_config.py", "core\\app_core.py", "database\\comprehensive_income_manager.py", "database\\fix_database.py", "ui\\daily_journal_window.py", "ui\\sales_analysis_window.py"]}