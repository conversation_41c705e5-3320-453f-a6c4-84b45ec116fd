# 🧾 وحدة الفواتير المتكاملة - برنامج ست الكل للمحاسبة

## نظرة عامة

تم تطوير وحدة فواتير شاملة ومتكاملة باللغة العربية مع دعم RTL كامل، تتضمن جميع أنواع الفواتير والتقارير المطلوبة مع واجهة حديثة وجذابة بصرياً.

## 🎯 الميزات الرئيسية

### 🧮 أنواع الفواتير المدعومة
- ✅ **فواتير البيع القياسية** - واجهة متكاملة مع جدول الأصناف والحساب التلقائي
- ✅ **نقطة البيع POS** - واجهة سريعة وعملية مع دعم الباركود والدفع المتعدد
- ✅ **فواتير الشراء** - إدارة المشتريات والموردين
- ✅ **مرتجع البيع** - إدارة مرتجعات العملاء
- ✅ **مرتجع الشراء** - إدارة مرتجعات الموردين
- ✅ **الفاتورة المختصرة** - للطباعة الحرارية السريعة

### 📊 التقارير والتحليل
- ✅ **تقارير الفواتير** مع فلترة متقدمة (التاريخ، النوع، المستخدم، العميل)
- ✅ **حركة الفواتير اليومية** (بيع / شراء / مرتجعات)
- ✅ **المبيعات اليومية للأصناف**
- ✅ **تحليل مبيعات الأصناف** (الأكثر مبيعاً، الأقل، إجمالي الربح)
- ✅ **تقرير مبيعات المستخدمين** (كل كاشير أو موظف بيع)
- ✅ **سلة المحذوفات** مع صلاحيات الاسترجاع

### 🎨 التصميم والواجهة
- ✅ **واجهة عربية RTL** كاملة مع خطوط أنيقة
- ✅ **لوحة تحكم Dashboard** مع كروت معلومات ديناميكية
- ✅ **شريط جانبي احترافي** للتنقل بين أنواع الفواتير
- ✅ **ألوان حديثة وجذابة** متناسقة مع التصميم العام
- ✅ **تصميم متجاوب** يتكيف مع أحجام الشاشات المختلفة

### 💾 قاعدة البيانات
- ✅ **جداول مترابطة** للفواتير والعملاء والمنتجات والموردين
- ✅ **نظام ترقيم تلقائي** للفواتير بصيغة ذكية (SAL-2024-07-00001)
- ✅ **تتبع حركات المخزون** التلقائي
- ✅ **نظام صلاحيات المستخدمين** (مشرف - كاشير - مدير)
- ✅ **حفظ واسترجاع** الفواتير القابلة للتعديل

### 🖨️ الطباعة والتصدير
- ✅ **طباعة حرارية** للإيصالات السريعة
- ✅ **طباعة A4** للفواتير الرسمية
- ✅ **تصدير PDF** مع تنسيق عربي احترافي
- ✅ **تصدير Excel** للتحليل والمعالجة

## 📁 هيكل الملفات

```
ui/
├── invoices_main_window.py      # النافذة الرئيسية للفواتير
├── sales_invoice_window.py      # نافذة فواتير البيع
├── pos_system_window.py         # نافذة نقطة البيع POS
└── invoices_reports_window.py   # نافذة التقارير والتحليل

database/
└── invoices_database_manager.py # مدير قاعدة البيانات للفواتير

services/
└── invoice_printer.py           # نظام الطباعة والتصدير
```

## 🚀 كيفية الاستخدام

### 1. فتح وحدة الفواتير
```python
# من النافذة الرئيسية، اضغط على زر "الفواتير" في الشريط الأخضر العلوي
# أو استخدم الكود التالي:
from ui.invoices_main_window import InvoicesMainWindow
invoices_window = InvoicesMainWindow()
```

### 2. إنشاء فاتورة بيع جديدة
1. اضغط على "🧾 فواتير البيع" من الشريط الجانبي
2. املأ معلومات العميل والفاتورة
3. أضف الأصناف باستخدام "➕ إضافة صنف" أو "📷 باركود"
4. احفظ الفاتورة باستخدام "💾 حفظ"

### 3. استخدام نقطة البيع POS
1. اضغط على "🛒 نقطة البيع POS"
2. امسح الباركود أو أدخل كود المنتج
3. اختر المنتجات من الشبكة السريعة
4. اختر طريقة الدفع وأتمم العملية

### 4. عرض التقارير
1. اضغط على "📊 التقارير والتحليل"
2. اختر نوع التقرير من الشريط الجانبي
3. طبق الفلاتر المطلوبة
4. صدر التقرير بصيغة PDF أو Excel

## 🔧 المتطلبات التقنية

### مكتبات Python المطلوبة
```bash
pip install customtkinter
pip install reportlab      # للتصدير إلى PDF
pip install openpyxl       # للتصدير إلى Excel
```

### قاعدة البيانات
- **SQLite** (افتراضي) - لا يتطلب إعداد إضافي
- **PostgreSQL** (اختياري) - للمشاريع الكبيرة

## 🎯 الميزات المتقدمة

### نظام الترقيم التلقائي
```python
# أمثلة على أرقام الفواتير المولدة تلقائياً:
SAL-2024-07-00045    # فاتورة بيع
PUR-2024-07-00012    # فاتورة شراء
RET-SAL-2024-07-00002 # مرتجع بيع
POS-20240719-0123    # نقطة بيع
```

### نظام الصلاحيات
- **المشرف**: الوصول الكامل لجميع الوظائف
- **الكاشير**: إنشاء وتعديل الفواتير فقط
- **المدير**: عرض التقارير والإحصائيات

### تتبع المخزون التلقائي
- تحديث الكميات تلقائياً عند إنشاء الفواتير
- تسجيل حركات المخزون مع التواريخ والمراجع
- تنبيهات عند نفاد المخزون

## 🔮 التطوير المستقبلي

### الميزات قيد التطوير
- [ ] **دعم الباركود الفعلي** مع قارئ الباركود
- [ ] **نظام الخصومات المتقدم** (خصم تدريجي، خصم كمية)
- [ ] **إدارة العروض والحملات**
- [ ] **تكامل مع أنظمة الدفع الإلكتروني**
- [ ] **تطبيق موبايل** للمبيعات الخارجية
- [ ] **لوحة تحكم تحليلية** مع الرسوم البيانية

### التحسينات المخططة
- [ ] **تحسين الأداء** لقواعد البيانات الكبيرة
- [ ] **نظام النسخ الاحتياطي التلقائي**
- [ ] **تصدير التقارير بصيغ إضافية** (Word, PowerPoint)
- [ ] **دعم العملات المتعددة**
- [ ] **نظام الإشعارات والتنبيهات**

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في تحميل الخطوط العربية
```python
# الحل: تأكد من وجود خطوط عربية في النظام
# أو ضع ملفات الخطوط في مجلد assets/fonts/
```

#### 2. خطأ في قاعدة البيانات
```python
# الحل: تأكد من صلاحيات الكتابة في مجلد database/
# أو قم بحذف ملف قاعدة البيانات وإعادة إنشائه
```

#### 3. مشاكل في الطباعة
```python
# الحل: تأكد من تثبيت مكتبات reportlab و openpyxl
pip install reportlab openpyxl
```

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملف السجلات في مجلد `logs/`
2. راجع هذا الدليل للحلول الشائعة
3. تواصل مع فريق التطوير

## 📄 الترخيص

هذه الوحدة جزء من برنامج ست الكل للمحاسبة وتخضع لنفس شروط الترخيص.

---

**تم تطوير هذه الوحدة بواسطة Augment Agent** 🤖
**التاريخ: 19 يوليو 2024**
