{"timestamp": "2025-07-18T23:58:08.877374", "duplicate_code": [{"code_sample": "def _generate_invoice_number(self) -> str:\n        \"\"\"إنشاء رقم فاتورة فريد\"\"\"\n        timestamp = d...", "occurrences": 2, "locations": [{"file": "database\\invoices_manager.py", "line": 442, "function": "def _generate_invoice_number(self) -> str:"}, {"file": "ui\\sales_manager.py", "line": 218, "function": "def _generate_invoice_number(self) -> str:"}]}, {"code_sample": "def save_invoice(self, customer_name: str, items: List[Dict], total_amount: float,\n                 ...", "occurrences": 2, "locations": [{"file": "services\\postgresql_sales_manager.py", "line": 25, "function": "def save_invoice(self, customer_name: str, items: List[Dict], total_amount: float,"}, {"file": "services\\sales_manager.py", "line": 41, "function": "def save_invoice(self, customer_name: str, items: List[Dict], total_amount: float,"}]}, {"code_sample": "def update_inventory(self, items: List[Dict]) -> Dict:\n        \"\"\"\n        تحديث المخزون بعد البيع\n ...", "occurrences": 2, "locations": [{"file": "services\\postgresql_sales_manager.py", "line": 153, "function": "def update_inventory(self, items: List[Dict]) -> Dict:"}, {"file": "services\\sales_manager.py", "line": 121, "function": "def update_inventory(self, items: List[Dict]) -> Dict:"}]}, {"code_sample": "def process_sale(self, customer_name: str, items: List[Dict], total_amount: float,\n                 ...", "occurrences": 2, "locations": [{"file": "services\\postgresql_sales_manager.py", "line": 235, "function": "def process_sale(self, customer_name: str, items: List[Dict], total_amount: float,"}, {"file": "services\\sales_manager.py", "line": 195, "function": "def process_sale(self, customer_name: str, items: List[Dict], total_amount: float,"}]}, {"code_sample": "def _validate_sale_data(self, customer_name: str, items: List[Dict], \n                           tot...", "occurrences": 2, "locations": [{"file": "services\\postgresql_sales_manager.py", "line": 419, "function": "def _validate_sale_data(self, customer_name: str, items: List[Dict],"}, {"file": "services\\sales_manager.py", "line": 332, "function": "def _validate_sale_data(self, customer_name: str, items: List[Dict],"}]}, {"code_sample": "def __init__(self, db_manager: DatabaseManager = None):\n        self.db_manager = db_manager or Data...", "occurrences": 2, "locations": [{"file": "services\\purchases_manager.py", "line": 16, "function": "def __init__(self, db_manager: DatabaseManager = None):"}, {"file": "services\\treasury_manager.py", "line": 16, "function": "def __init__(self, db_manager: DatabaseManager = None):"}]}, {"code_sample": "def create_window(self):\n        \"\"\"إنشاء النافذة الرئيسية\"\"\"\n        self.window = ctk.CTkToplevel(...", "occurrences": 11, "locations": [{"file": "ui\\accounts_tree_window.py", "line": 29, "function": "def create_window(self):"}, {"file": "ui\\advanced_financial_reports_window.py", "line": 479, "function": "def create_window(self):"}, {"file": "ui\\categories_management_window.py", "line": 48, "function": "def create_window(self):"}, {"file": "ui\\comprehensive_income_window.py", "line": 41, "function": "def create_window(self):"}, {"file": "ui\\daily_journal_window.py", "line": 50, "function": "def create_window(self):"}, {"file": "ui\\sales_analysis_window.py", "line": 48, "function": "def create_window(self):"}, {"file": "ui\\stock_management_window.py", "line": 50, "function": "def create_window(self):"}, {"file": "ui\\structured_profit_loss_window.py", "line": 41, "function": "def create_window(self):"}, {"file": "ui\\units_management_window.py", "line": 48, "function": "def create_window(self):"}, {"file": "ui\\warehouses_management_window.py", "line": 48, "function": "def create_window(self):"}, {"file": "ui\\warehouse_management_window.py", "line": 41, "function": "def create_window(self):"}]}, {"code_sample": "def create_header(self):\n        \"\"\"إنشاء رأس النافذة\"\"\"\n        header_frame = ctk.CTkFrame(self.wi...", "occurrences": 6, "locations": [{"file": "ui\\accounts_tree_window.py", "line": 50, "function": "def create_header(self):"}, {"file": "ui\\comprehensive_income_window.py", "line": 63, "function": "def create_header(self):"}, {"file": "ui\\sales_analysis_window.py", "line": 396, "function": "def create_header(self):"}, {"file": "ui\\sales_window.py", "line": 78, "function": "def create_header(self):"}, {"file": "ui\\structured_profit_loss_window.py", "line": 63, "function": "def create_header(self):"}, {"file": "ui\\warehouse_management_window.py", "line": 62, "function": "def create_header(self):"}]}, {"code_sample": "def create_main_content(self):\n        \"\"\"إنشاء المحتوى الرئيسي\"\"\"\n        main_frame = ctk.CTkFrame...", "occurrences": 8, "locations": [{"file": "ui\\accounts_tree_window.py", "line": 85, "function": "def create_main_content(self):"}, {"file": "ui\\accounts_window.py", "line": 65, "function": "def create_main_content(self):"}, {"file": "ui\\advanced_financial_reports_window.py", "line": 540, "function": "def create_main_content(self):"}, {"file": "ui\\inventory_window.py", "line": 104, "function": "def create_main_content(self):"}, {"file": "ui\\purchases_window.py", "line": 69, "function": "def create_main_content(self):"}, {"file": "ui\\reports_window.py", "line": 64, "function": "def create_main_content(self):"}, {"file": "ui\\sales_window.py", "line": 116, "function": "def create_main_content(self):"}, {"file": "ui\\treasury_window.py", "line": 110, "function": "def create_main_content(self):"}]}, {"code_sample": "def create_buttons(self):\n        \"\"\"إنشاء أزرار النافذة\"\"\"\n        buttons_frame = ctk.CTkFrame(sel...", "occurrences": 5, "locations": [{"file": "ui\\accounts_tree_window.py", "line": 272, "function": "def create_buttons(self):"}, {"file": "ui\\comprehensive_income_window.py", "line": 239, "function": "def create_buttons(self):"}, {"file": "ui\\sales_analysis_window.py", "line": 634, "function": "def create_buttons(self):"}, {"file": "ui\\structured_profit_loss_window.py", "line": 251, "function": "def create_buttons(self):"}, {"file": "ui\\warehouse_management_window.py", "line": 414, "function": "def create_buttons(self):"}]}, {"code_sample": "def close_window(self):\n        \"\"\"إغلاق النافذة\"\"\"\n        self.window.destroy()...", "occurrences": 18, "locations": [{"file": "ui\\accounts_tree_window.py", "line": 511, "function": "def close_window(self):"}, {"file": "ui\\accounts_window.py", "line": 421, "function": "def close_window(self):"}, {"file": "ui\\advanced_financial_reports_window.py", "line": 1288, "function": "def close_window(self):"}, {"file": "ui\\backup_restore.py", "line": 710, "function": "def close_window(self):"}, {"file": "ui\\categories_management_window.py", "line": 721, "function": "def close_window(self):"}, {"file": "ui\\comprehensive_income_window.py", "line": 612, "function": "def close_window(self):"}, {"file": "ui\\comprehensive_sales_window.py", "line": 496, "function": "def close_window(self):"}, {"file": "ui\\daily_journal_window.py", "line": 937, "function": "def close_window(self):"}, {"file": "ui\\inventory_window.py", "line": 669, "function": "def close_window(self):"}, {"file": "ui\\journal_entries_window.py", "line": 478, "function": "def close_window(self):"}, {"file": "ui\\purchases_window.py", "line": 498, "function": "def close_window(self):"}, {"file": "ui\\reports_window.py", "line": 524, "function": "def close_window(self):"}, {"file": "ui\\sales_window.py", "line": 519, "function": "def close_window(self):"}, {"file": "ui\\structured_profit_loss_window.py", "line": 705, "function": "def close_window(self):"}, {"file": "ui\\treasury_window.py", "line": 607, "function": "def close_window(self):"}, {"file": "ui\\units_management_window.py", "line": 585, "function": "def close_window(self):"}, {"file": "ui\\user_management.py", "line": 535, "function": "def close_window(self):"}, {"file": "ui\\warehouse_management_window.py", "line": 829, "function": "def close_window(self):"}]}, {"code_sample": "def create_buttons(self, parent):\n        \"\"\"إنشاء أزرار العمليات\"\"\"\n        buttons_frame = ctk.CTk...", "occurrences": 2, "locations": [{"file": "ui\\accounts_window.py", "line": 232, "function": "def create_buttons(self, parent):"}, {"file": "ui\\reports_window.py", "line": 203, "function": "def create_buttons(self, parent):"}]}, {"code_sample": "def create_header(self):\n        \"\"\"إنشاء رأس النافذة\"\"\"\n        header_frame = ctk.CTkFrame(self.wi...", "occurrences": 5, "locations": [{"file": "ui\\add_items_window.py", "line": 70, "function": "def create_header(self):"}, {"file": "ui\\categories_management_window.py", "line": 79, "function": "def create_header(self):"}, {"file": "ui\\daily_journal_window.py", "line": 73, "function": "def create_header(self):"}, {"file": "ui\\units_management_window.py", "line": 79, "function": "def create_header(self):"}, {"file": "ui\\warehouses_management_window.py", "line": 79, "function": "def create_header(self):"}]}, {"code_sample": "def create_main_layout(self):\n        \"\"\"إنشاء التخطيط الرئيسي\"\"\"\n        # إطار المحتوى الرئيسي\n   ...", "occurrences": 4, "locations": [{"file": "ui\\add_items_window.py", "line": 116, "function": "def create_main_layout(self):"}, {"file": "ui\\categories_management_window.py", "line": 125, "function": "def create_main_layout(self):"}, {"file": "ui\\units_management_window.py", "line": 117, "function": "def create_main_layout(self):"}, {"file": "ui\\warehouses_management_window.py", "line": 125, "function": "def create_main_layout(self):"}]}, {"code_sample": "def create_checkbox_field(self, parent, label_text, field_name):\n        \"\"\"إنشاء حقل مربع اختيار\"\"\"...", "occurrences": 2, "locations": [{"file": "ui\\add_items_window.py", "line": 330, "function": "def create_checkbox_field(self, parent, label_text, field_name):"}, {"file": "ui\\warehouses_management_window.py", "line": 272, "function": "def create_checkbox_field(self, parent, label_text, field_name):"}]}, {"code_sample": "def create_textarea_field(self, parent, label_text, field_name):\n        \"\"\"إنشاء حقل نص متعدد الأسط...", "occurrences": 2, "locations": [{"file": "ui\\add_items_window.py", "line": 350, "function": "def create_textarea_field(self, parent, label_text, field_name):"}, {"file": "ui\\warehouses_management_window.py", "line": 244, "function": "def create_textarea_field(self, parent, label_text, field_name):"}]}, {"code_sample": "def create_window(self):\n        \"\"\"إنشاء النافذة\"\"\"\n        self.window = ctk.CTkToplevel(self.pare...", "occurrences": 3, "locations": [{"file": "ui\\backup_restore.py", "line": 32, "function": "def create_window(self):"}, {"file": "ui\\journal_entries_window.py", "line": 30, "function": "def create_window(self):"}, {"file": "ui\\user_management.py", "line": 41, "function": "def create_window(self):"}]}, {"code_sample": "def center_window(self):\n        \"\"\"توسيط النافذة\"\"\"\n        self.window.update_idletasks()\n        ...", "occurrences": 2, "locations": [{"file": "ui\\backup_restore.py", "line": 48, "function": "def center_window(self):"}, {"file": "ui\\user_management.py", "line": 57, "function": "def center_window(self):"}]}, {"code_sample": "def create_content(self):\n        \"\"\"إنشاء محتوى النافذة\"\"\"\n        # الإطار الرئيسي\n        main_fr...", "occurrences": 2, "locations": [{"file": "ui\\backup_restore.py", "line": 57, "function": "def create_content(self):"}, {"file": "ui\\user_management.py", "line": 66, "function": "def create_content(self):"}]}, {"code_sample": "def create_form_field(self, parent, label_text, field_name, required=False):\n        \"\"\"إنشاء حقل في...", "occurrences": 2, "locations": [{"file": "ui\\categories_management_window.py", "line": 169, "function": "def create_form_field(self, parent, label_text, field_name, required=False):"}, {"file": "ui\\units_management_window.py", "line": 170, "function": "def create_form_field(self, parent, label_text, field_name, required=False):"}]}, {"code_sample": "def create_form_buttons(self, parent):\n        \"\"\"إنشاء أزرار النموذج\"\"\"\n        buttons_frame = ctk...", "occurrences": 3, "locations": [{"file": "ui\\categories_management_window.py", "line": 226, "function": "def create_form_buttons(self, parent):"}, {"file": "ui\\units_management_window.py", "line": 200, "function": "def create_form_buttons(self, parent):"}, {"file": "ui\\warehouses_management_window.py", "line": 292, "function": "def create_form_buttons(self, parent):"}]}, {"code_sample": "def create_controls(self):\n        \"\"\"إنشاء عناصر التحكم\"\"\"\n        controls_frame = ctk.CTkFrame(se...", "occurrences": 2, "locations": [{"file": "ui\\comprehensive_income_window.py", "line": 98, "function": "def create_controls(self):"}, {"file": "ui\\structured_profit_loss_window.py", "line": 98, "function": "def create_controls(self):"}]}, {"code_sample": "def set_current_month(self):\n        \"\"\"تعيين الشهر الحالي\"\"\"\n        today = date.today()\n        s...", "occurrences": 2, "locations": [{"file": "ui\\comprehensive_income_window.py", "line": 295, "function": "def set_current_month(self):"}, {"file": "ui\\structured_profit_loss_window.py", "line": 307, "function": "def set_current_month(self):"}]}, {"code_sample": "def set_current_year(self):\n        \"\"\"تعيين العام الحالي\"\"\"\n        today = date.today()\n        st...", "occurrences": 2, "locations": [{"file": "ui\\comprehensive_income_window.py", "line": 307, "function": "def set_current_year(self):"}, {"file": "ui\\structured_profit_loss_window.py", "line": 319, "function": "def set_current_year(self):"}]}, {"code_sample": "def set_current_quarter(self):\n        \"\"\"تعيين الربع الحالي\"\"\"\n        today = date.today()\n       ...", "occurrences": 2, "locations": [{"file": "ui\\comprehensive_income_window.py", "line": 319, "function": "def set_current_quarter(self):"}, {"file": "ui\\structured_profit_loss_window.py", "line": 331, "function": "def set_current_quarter(self):"}]}, {"code_sample": "def create_window(self):\n        \"\"\"إنشاء النافذة الرئيسية\"\"\"\n        try:\n            self.window =...", "occurrences": 5, "locations": [{"file": "ui\\comprehensive_sales_window.py", "line": 48, "function": "def create_window(self):"}, {"file": "ui\\employees_window.py", "line": 78, "function": "def create_window(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 33, "function": "def create_window(self):"}, {"file": "ui\\enhanced_pos_window.py", "line": 115, "function": "def create_window(self):"}, {"file": "ui\\pos_simple.py", "line": 70, "function": "def create_window(self):"}]}, {"code_sample": "def create_main_layout(self):\n        \"\"\"إنشاء التخطيط الرئيسي\"\"\"\n        # الشريط العلوي\n        se...", "occurrences": 2, "locations": [{"file": "ui\\comprehensive_sales_window.py", "line": 66, "function": "def create_main_layout(self):"}, {"file": "ui\\enhanced_pos_window.py", "line": 133, "function": "def create_main_layout(self):"}]}, {"code_sample": "def __init__(self, parent):\n        self.parent = parent\n        self.window = None\n        self.cur...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 66, "function": "def __init__(self, parent):"}, {"file": "ui\\employees_window_fixed.py", "line": 21, "function": "def __init__(self, parent):"}]}, {"code_sample": "def init_form_variables(self):\n        \"\"\"تهيئة متغيرات النموذج\"\"\"\n        self.form_vars = {\n      ...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 101, "function": "def init_form_variables(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 57, "function": "def init_form_variables(self):"}]}, {"code_sample": "def create_personal_tab(self):\n        \"\"\"إنشاء تبويب البيانات الشخصية\"\"\"\n        tab = self.noteboo...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 290, "function": "def create_personal_tab(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 232, "function": "def create_personal_tab(self):"}]}, {"code_sample": "def create_contact_tab(self):\n        \"\"\"إنشاء تبويب بيانات الاتصال\"\"\"\n        tab = self.notebook.t...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 359, "function": "def create_contact_tab(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 301, "function": "def create_contact_tab(self):"}]}, {"code_sample": "def create_policy_tab(self):\n        \"\"\"إنشاء تبويب بيانات السياسة\"\"\"\n        tab = self.notebook.ta...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 371, "function": "def create_policy_tab(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 313, "function": "def create_policy_tab(self):"}]}, {"code_sample": "def create_settings_tab(self):\n        \"\"\"إنشاء تبويب إعدادات المستخدم\"\"\"\n        tab = self.noteboo...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 529, "function": "def create_settings_tab(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 353, "function": "def create_settings_tab(self):"}]}, {"code_sample": "def create_field(self, parent, label_text, variable, width=200):\n        \"\"\"إنشاء حقل أفقي\"\"\"\n      ...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 567, "function": "def create_field(self, parent, label_text, variable, width=200):"}, {"file": "ui\\employees_window_fixed.py", "line": 391, "function": "def create_field(self, parent, label_text, variable, width=200):"}]}, {"code_sample": "def create_field_vertical(self, parent, label_text, variable, width=200):\n        \"\"\"إنشاء حقل عمودي...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 578, "function": "def create_field_vertical(self, parent, label_text, variable, width=200):"}, {"file": "ui\\employees_window_fixed.py", "line": 402, "function": "def create_field_vertical(self, parent, label_text, variable, width=200):"}]}, {"code_sample": "def create_combobox(self, parent, label_text, variable, values, width=200):\n        \"\"\"إنشاء قائمة م...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 589, "function": "def create_combobox(self, parent, label_text, variable, values, width=200):"}, {"file": "ui\\employees_window_fixed.py", "line": 413, "function": "def create_combobox(self, parent, label_text, variable, values, width=200):"}]}, {"code_sample": "def create_combobox_vertical(self, parent, label_text, variable, values, width=200):\n        \"\"\"إنشا...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 600, "function": "def create_combobox_vertical(self, parent, label_text, variable, values, width=200):"}, {"file": "ui\\employees_window_fixed.py", "line": 424, "function": "def create_combobox_vertical(self, parent, label_text, variable, values, width=200):"}]}, {"code_sample": "def select_employee_photo(self):\n        \"\"\"اختيار صورة الموظف\"\"\"\n        try:\n            file_path...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 674, "function": "def select_employee_photo(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 495, "function": "def select_employee_photo(self):"}]}, {"code_sample": "def delete_employee_photo(self):\n        \"\"\"حذف صورة الموظف\"\"\"\n        try:\n            self.photo_p...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 690, "function": "def delete_employee_photo(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 511, "function": "def delete_employee_photo(self):"}]}, {"code_sample": "def save_employee_data(self):\n        \"\"\"حفظ بيانات الموظف\"\"\"\n        try:\n            if not self.f...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 700, "function": "def save_employee_data(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 521, "function": "def save_employee_data(self):"}]}, {"code_sample": "def cancel_operation(self):\n        \"\"\"إلغاء العملية\"\"\"\n        try:\n            result = messagebox...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 754, "function": "def cancel_operation(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 534, "function": "def cancel_operation(self):"}]}, {"code_sample": "def new_employee(self):\n        \"\"\"موظف جديد\"\"\"\n        try:\n            self.clear_form()\n         ...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 764, "function": "def new_employee(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 544, "function": "def new_employee(self):"}]}, {"code_sample": "def delete_employee(self):\n        \"\"\"حذف الموظف\"\"\"\n        try:\n            result = messagebox.ask...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 773, "function": "def delete_employee(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 553, "function": "def delete_employee(self):"}]}, {"code_sample": "def clear_form(self):\n        \"\"\"مسح النموذج\"\"\"\n        try:\n            for var in self.form_vars.v...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 784, "function": "def clear_form(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 564, "function": "def clear_form(self):"}]}, {"code_sample": "def close_window(self):\n        \"\"\"إغلاق النافذة\"\"\"\n        try:\n            result = messagebox.ask...", "occurrences": 2, "locations": [{"file": "ui\\employees_window.py", "line": 867, "function": "def close_window(self):"}, {"file": "ui\\employees_window_fixed.py", "line": 585, "function": "def close_window(self):"}]}, {"code_sample": "def __init__(self, parent):\n        self.parent = parent\n        self.window = None\n        self.car...", "occurrences": 2, "locations": [{"file": "ui\\enhanced_pos_window.py", "line": 33, "function": "def __init__(self, parent):"}, {"file": "ui\\pos_simple.py", "line": 14, "function": "def __init__(self, parent):"}]}, {"code_sample": "def process_payment(self):\n        \"\"\"معالجة عملية الدفع\"\"\"\n        if not self.cart_items:\n        ...", "occurrences": 2, "locations": [{"file": "ui\\enhanced_pos_window.py", "line": 849, "function": "def process_payment(self):"}, {"file": "ui\\pos_window.py", "line": 743, "function": "def process_payment(self):"}]}, {"code_sample": "def load_products(self):\n        \"\"\"جلب المنتجات من قاعدة البيانات\"\"\"\n        try:\n            if se...", "occurrences": 2, "locations": [{"file": "ui\\pos_simple.py", "line": 35, "function": "def load_products(self):"}, {"file": "ui\\sales_window.py", "line": 38, "function": "def load_products(self):"}]}, {"code_sample": "def remove_item(self, index):\n        \"\"\"حذف عنصر من السلة\"\"\"\n        if 0 <= index < len(self.cart_...", "occurrences": 2, "locations": [{"file": "ui\\pos_simple.py", "line": 262, "function": "def remove_item(self, index):"}, {"file": "ui\\pos_window.py", "line": 596, "function": "def remove_item(self, index):"}]}, {"code_sample": "def close_window(self):\n        \"\"\"إغلاق النافذة\"\"\"\n        if self.cart_items:\n            if messa...", "occurrences": 2, "locations": [{"file": "ui\\pos_simple.py", "line": 347, "function": "def close_window(self):"}, {"file": "ui\\pos_window.py", "line": 826, "function": "def close_window(self):"}]}, {"code_sample": "def create_totals_section(self, parent):\n        \"\"\"إنشاء قسم الإجماليات\"\"\"\n        totals_frame = c...", "occurrences": 2, "locations": [{"file": "ui\\purchases_window.py", "line": 209, "function": "def create_totals_section(self, parent):"}, {"file": "ui\\sales_window.py", "line": 250, "function": "def create_totals_section(self, parent):"}]}, {"code_sample": "def create_footer(self):\n        \"\"\"إنشاء تذييل النافذة\"\"\"\n        footer_frame = ctk.CTkFrame(self....", "occurrences": 2, "locations": [{"file": "ui\\purchases_window.py", "line": 236, "function": "def create_footer(self):"}, {"file": "ui\\sales_window.py", "line": 277, "function": "def create_footer(self):"}]}, {"code_sample": "def create_main_content(self):\n        \"\"\"إنشاء المحتوى الرئيسي\"\"\"\n        main_frame = ctk.CTkFrame...", "occurrences": 2, "locations": [{"file": "ui\\sales_analysis_window.py", "line": 510, "function": "def create_main_content(self):"}, {"file": "ui\\warehouse_management_window.py", "line": 97, "function": "def create_main_content(self):"}]}, {"code_sample": "def create_welcome_window(self):\n        \"\"\"إنشاء نافذة الترحيب\"\"\"\n        # إنشاء النافذة\n        i...", "occurrences": 2, "locations": [{"file": "ui\\simple_welcome_window.py", "line": 23, "function": "def create_welcome_window(self):"}, {"file": "ui\\welcome_window.py", "line": 44, "function": "def create_welcome_window(self):"}]}, {"code_sample": "def create_default_image(self, parent):\n        \"\"\"إنشاء صورة افتراضية\"\"\"\n        # إطار دائري للصور...", "occurrences": 2, "locations": [{"file": "ui\\simple_welcome_window.py", "line": 251, "function": "def create_default_image(self, parent):"}, {"file": "ui\\welcome_window.py", "line": 168, "function": "def create_default_image(self, parent):"}]}, {"code_sample": "def start_loading(self):\n        \"\"\"بدء عملية التحميل\"\"\"\n        def loading_animation():\n          ...", "occurrences": 2, "locations": [{"file": "ui\\simple_welcome_window.py", "line": 275, "function": "def start_loading(self):"}, {"file": "ui\\welcome_window.py", "line": 387, "function": "def start_loading(self):"}]}, {"code_sample": "def loading_animation():\n            loading_texts = [\n                \"🔄 تهيئة النظام...\",\n        ...", "occurrences": 2, "locations": [{"file": "ui\\simple_welcome_window.py", "line": 277, "function": "def loading_animation():"}, {"file": "ui\\welcome_window.py", "line": 389, "function": "def loading_animation():"}]}], "unused_imports": [{"file": "cleanup_unnecessary_files.py", "import": "import os", "module": "os"}, {"file": "cleanup_unnecessary_files.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "comprehensive_income_formula_demo.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "database_analyzer.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "main.py", "import": "import os", "module": "os"}, {"file": "main.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "performance_optimizer.py", "import": "import os", "module": "os"}, {"file": "performance_optimizer.py", "import": "import ast", "module": "ast"}, {"file": "performance_optimizer.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "performance_optimizer.py", "import": "from collections import defaultdict", "module": "collections"}, {"file": "run_app.py", "import": "import os", "module": "os"}, {"file": "run_app.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "run_fixed_app.py", "import": "import os", "module": "os"}, {"file": "run_fixed_app.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "safe_start.py", "import": "import os", "module": "os"}, {"file": "safe_start.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "setup.py", "import": "from setuptools import setup, find_packages", "module": "setuptools"}, {"file": "start_with_scheduler.py", "import": "import os", "module": "os"}, {"file": "start_with_scheduler.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "config\\arabic_fonts.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "config\\postgresql_config.py", "import": "from typing import Dict", "module": "typing"}, {"file": "config\\scheduler_settings.py", "import": "from datetime import time", "module": "datetime"}, {"file": "config\\settings.py", "import": "import os", "module": "os"}, {"file": "config\\settings.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "config\\sqlserver_config.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "core\\app_core.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "core\\barcode_scanner.py", "import": "from typing import Optional, Callable", "module": "typing"}, {"file": "core\\error_handler.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "core\\error_handler.py", "import": "from typing import Optional, Any, Dict", "module": "typing"}, {"file": "core\\scheduler_manager.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "database\\accounts_manager.py", "import": "from typing import Dict, List, Optional", "module": "typing"}, {"file": "database\\accounts_tree_manager.py", "import": "from typing import Dict, List, Optional, Tuple", "module": "typing"}, {"file": "database\\comprehensive_income_manager.py", "import": "from typing import Dict, List, Optional, Tuple", "module": "typing"}, {"file": "database\\database_manager.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "database\\fix_database.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "database\\fix_database.py", "import": "from datetime import datetime", "module": "datetime"}, {"file": "database\\hybrid_database_manager.py", "import": "from typing import Dict, List, Optional, Any", "module": "typing"}, {"file": "database\\hybrid_database_manager.py", "import": "from enum import Enum", "module": "enum"}, {"file": "database\\invoices_manager.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "database\\invoices_manager.py", "import": "from typing import Dict, List, Optional, Tuple", "module": "typing"}, {"file": "database\\journal_entries_manager.py", "import": "from typing import Dict, List, Optional", "module": "typing"}, {"file": "database\\postgresql_manager.py", "import": "from typing import Dict, List, Optional, Any", "module": "typing"}, {"file": "database\\postgresql_manager.py", "import": "from contextlib import contextmanager", "module": "contextlib"}, {"file": "database\\postgresql_manager.py", "import": "import json", "module": "json"}, {"file": "database\\products_manager.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "database\\products_manager.py", "import": "from datetime import datetime", "module": "datetime"}, {"file": "database\\products_manager.py", "import": "from typing import Dict, List, Optional, Tuple", "module": "typing"}, {"file": "database\\profit_loss_structure_manager.py", "import": "from typing import Dict, List, Optional, Tuple", "module": "typing"}, {"file": "database\\reports_manager.py", "import": "from typing import Dict, List, Optional", "module": "typing"}, {"file": "database\\reports_manager.py", "import": "from datetime import datetime, date, timedelta", "module": "datetime"}, {"file": "database\\sqlserver_manager.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "database\\sqlserver_manager.py", "import": "from typing import Optional, Dict, List, Any", "module": "typing"}, {"file": "database\\warehouse_manager.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "database\\warehouse_manager.py", "import": "from typing import Dict, List, Optional, Tuple", "module": "typing"}, {"file": "reports\\report_generator.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "services\\employees_manager.py", "import": "from typing import Dict, List, Optional", "module": "typing"}, {"file": "services\\employees_manager.py", "import": "from datetime import datetime, date", "module": "datetime"}, {"file": "services\\postgresql_sales_manager.py", "import": "from typing import Dict, List, Optional, Tuple", "module": "typing"}, {"file": "services\\purchases_manager.py", "import": "from typing import Dict, List, Optional", "module": "typing"}, {"file": "services\\sales_manager.py", "import": "from typing import Dict, List, Optional, Tuple", "module": "typing"}, {"file": "services\\sales_manager.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "services\\treasury_manager.py", "import": "from typing import Dict, List, Optional", "module": "typing"}, {"file": "services\\treasury_manager.py", "import": "from datetime import datetime, date", "module": "datetime"}, {"file": "themes\\font_manager.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "themes\\font_manager.py", "import": "from typing import Dict, List, Optional, Tuple", "module": "typing"}, {"file": "themes\\theme_manager.py", "import": "import json", "module": "json"}, {"file": "themes\\theme_manager.py", "import": "import os", "module": "os"}, {"file": "themes\\theme_manager.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "ui\\accounts_tree_window.py", "import": "from typing import Dict, List, Optional", "module": "typing"}, {"file": "ui\\accounts_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\add_items_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\add_items_window.py", "import": "from typing import List, Dict, Optional, Tuple", "module": "typing"}, {"file": "ui\\advanced_financial_reports_window.py", "import": "from datetime import datetime, timedelta", "module": "datetime"}, {"file": "ui\\advanced_financial_reports_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\advanced_financial_reports_window.py", "import": "from PIL import Image, ImageTk", "module": "PIL"}, {"file": "ui\\backup_restore.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "ui\\categories_management_window.py", "import": "from datetime import datetime", "module": "datetime"}, {"file": "ui\\categories_management_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\categories_management_window.py", "import": "from typing import List, Dict, Optional, Tuple", "module": "typing"}, {"file": "ui\\daily_journal_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\daily_journal_window.py", "import": "from typing import List, Dict, Optional, Tuple", "module": "typing"}, {"file": "ui\\employees_window.py", "import": "from datetime import datetime", "module": "datetime"}, {"file": "ui\\employees_window_fixed.py", "import": "from datetime import datetime", "module": "datetime"}, {"file": "ui\\enhanced_pos_window.py", "import": "import math", "module": "math"}, {"file": "ui\\enhanced_pos_window.py", "import": "from PIL import Image, ImageTk", "module": "PIL"}, {"file": "ui\\inventory_window.py", "import": "from datetime import datetime", "module": "datetime"}, {"file": "ui\\inventory_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\journal_entries_window.py", "import": "from datetime import datetime, date", "module": "datetime"}, {"file": "ui\\journal_entries_window.py", "import": "from typing import Dict, List", "module": "typing"}, {"file": "ui\\main_window.py", "import": "from PIL import Image", "module": "PIL"}, {"file": "ui\\purchases_window.py", "import": "from typing import Dict, List", "module": "typing"}, {"file": "ui\\reports_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\sales_manager.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\sales_manager.py", "import": "from typing import Dict, List, Optional", "module": "typing"}, {"file": "ui\\sales_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\simple_welcome_window.py", "import": "from PIL import Image, ImageTk, ImageDraw", "module": "PIL"}, {"file": "ui\\simple_welcome_window.py", "import": "from pathlib import Path", "module": "pathlib"}, {"file": "ui\\stock_management_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\stock_management_window.py", "import": "from typing import List, Dict, Optional, Tuple", "module": "typing"}, {"file": "ui\\treasury_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\units_management_window.py", "import": "from datetime import datetime", "module": "datetime"}, {"file": "ui\\units_management_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\units_management_window.py", "import": "from typing import List, Dict, Optional, Tuple", "module": "typing"}, {"file": "ui\\warehouses_management_window.py", "import": "from datetime import datetime", "module": "datetime"}, {"file": "ui\\warehouses_management_window.py", "import": "import sqlite3", "module": "sqlite3"}, {"file": "ui\\warehouses_management_window.py", "import": "from typing import List, Dict, Optional, Tuple", "module": "typing"}, {"file": "ui\\warehouse_management_window.py", "import": "from datetime import datetime, date", "module": "datetime"}, {"file": "ui\\welcome_window.py", "import": "from PIL import Image, ImageTk, ImageDraw, ImageFilter", "module": "PIL"}, {"file": "ui\\welcome_window.py", "import": "from pathlib import Path", "module": "pathlib"}], "unused_variables": [], "performance_issues": [{"file": "ui\\add_items_window.py", "line": 1528, "issue": "open(image_path)", "suggestion": "استخدم with statement لف<PERSON><PERSON> الملفات"}, {"file": "ui\\enhanced_pos_window.py", "line": 65, "issue": "open(image_path)", "suggestion": "استخدم with statement لف<PERSON><PERSON> الملفات"}, {"file": "ui\\main_window.py", "line": 878, "issue": "open(icon_path)", "suggestion": "استخدم with statement لف<PERSON><PERSON> الملفات"}, {"file": "ui\\main_window.py", "line": 933, "issue": "open(icon_path)", "suggestion": "استخدم with statement لف<PERSON><PERSON> الملفات"}, {"file": "ui\\main_window.py", "line": 1932, "issue": "open(\"https://facebook.com\")", "suggestion": "استخدم with statement لف<PERSON><PERSON> الملفات"}, {"file": "ui\\main_window.py", "line": 1937, "issue": "open(\"https://youtube.com\")", "suggestion": "استخدم with statement لف<PERSON><PERSON> الملفات"}, {"file": "ui\\main_window.py", "line": 1942, "issue": "open(\"https://example.com\")", "suggestion": "استخدم with statement لف<PERSON><PERSON> الملفات"}, {"file": "ui\\main_window.py", "line": 1947, "issue": "open(\"mailto:<EMAIL>\")", "suggestion": "استخدم with statement لف<PERSON><PERSON> الملفات"}, {"file": "ui\\simple_welcome_window.py", "line": 204, "issue": "open(image_path)", "suggestion": "استخدم with statement لف<PERSON><PERSON> الملفات"}, {"file": "ui\\simple_welcome_window.py", "line": 302, "issue": "time.sleep(1)", "suggestion": "تجنب استخدام sleep طويل في الواجهة الرئيسية"}, {"file": "ui\\welcome_window.py", "line": 194, "issue": "open(image_path)", "suggestion": "استخدم with statement لف<PERSON><PERSON> الملفات"}, {"file": "ui\\welcome_window.py", "line": 410, "issue": "time.sleep(1)", "suggestion": "تجنب استخدام sleep طويل في الواجهة الرئيسية"}], "recommendations": ["حذف 109 استيراد غير مستخدم", "إعادة هيكلة 57 قطعة كود مكررة", "إصلاح 12 مشكلة أداء", "استخدام lazy loading للوحدات الكبيرة", "تحسين استعلامات قاعدة البيانات", "استخدام connection pooling", "تحسين تحميل الصور والأيقونات", "إضافة caching للعمليات المتكررة"]}