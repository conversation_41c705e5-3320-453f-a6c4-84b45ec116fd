{"timestamp": "2025-07-18T23:52:01.256249", "deleted_files": ["test_accounting_system.py", "test_accounts_tree.py", "test_accounts_tree_simple.py", "test_add_items.py", "test_all_windows.py", "test_arabic_fonts.py", "test_auth.py", "test_barcode_scanner.py", "test_chart_generation.py", "test_comprehensive_income.py", "test_daily_journal.py", "test_dropdown_menu.py", "test_excel_generation.py", "test_fetch_all_function.py", "test_fullscreen_windows.py", "test_integrated_system.py", "test_items_window.py", "test_main_dropdown_fix.py", "test_main_window_fixed.py", "test_management_windows.py", "test_new_login.py", "test_new_system.py", "test_pdf_generation.py", "test_professional_dropdown.py", "test_purchases_system.py", "test_sales_analysis.py", "test_scheduler.py", "test_sqlserver.py", "test_stock_management.py", "test_structured_profit_loss.py", "test_warehouse_management.py", "اختبار_أزرار_الأصناف.py", "اختبار_ألوان_الواجهة_الرئيسية.py", "اختبار_أيقونة_الموظفين.py", "اختبار_إدارة_الأصناف.py", "اختبار_إصلاح_نافذة_الموظفين.py", "اختبار_الألوان_الجذابة.py", "اختبار_الألوان_المبدلة.py", "اختبار_الألوان_المتألقة.py", "اختبار_الألوان_الملونة.py", "اختبار_البرنامج_المصحح.py", "اختبار_البيانات_الجديدة.py", "اختبار_التعديلات_الجديدة.py", "اختبار_الروابط_المحدثة.py", "اختبار_القائمة_المبسط.py", "اختبار_القائمة_المنسدلة_الرئيسية.py", "اختبار_الكود_المصحح.py", "اختبار_النوافذ_الجديدة.py", "اختبار_حذف_العنصر_الفارغ.py", "اختبار_سريع_للأخطاء.py", "اختبار_قاعدة_البيانات_بعد_الإصلاح.py", "اختبار_نافذة_البيع_المحسنة.py", "اختبار_نافذة_الترحيب.py", "اختبار_نافذة_الترحيب_المبسطة.py", "اختبار_نافذة_المبيعات_الشاملة.py", "اختبار_نافذة_المخزون_المحدثة.py", "اختبار_نافذة_الموظفين_الجديدة.py", "اختبار_نافذة_الموظفين_المصححة.py", "basic_test.py", "comprehensive_test.py", "quick_test.py", "simple_auth_test.py", "simple_stock_test.py", "فحص_جودة_البيانات.py", "فحص_وإصلاح_قاعدة_البيانات_الشامل.py", "فحص_وتشغيل_شامل.py", "إصلاح_البيانات_المتقدم.py", "إصلاح_شامل_للبرنامج.py", "إصلاح_قاعدة_البيانات.py", "تشغيل_البرنامج.py", "تشغيل_البرنامج_المصحح.py", "تشغيل_سريع.py", "تشغيل_مع_إصلاح.py", "system_checker.py", "system_fixer.py", "تقرير_ألوان_الواجهة_الرئيسية_النهائي.md", "تقرير_إصلاح_fetch_all.md", "تقرير_إصلاح_الأيقونات.md", "تقرير_إصلاح_العربية_والخطوط.md", "تقرير_إصلاح_القائمة_المنسدلة_النهائي.md", "تقرير_إصلاح_القائمة_النهائي.md", "تقرير_إصلاح_النافذة_الرئيسية_النهائي.md", "تقرير_إصلاح_خطأ_القائمة_الرئيسية.md", "تقرير_إصلاح_نافذة_الأصناف.md", "تقرير_إصلاح_نافذة_التقارير_المالية.md", "تقرير_إصلاح_نافذة_الموظفين.md", "تقرير_إضافة_صور_المنتجات.md", "تقرير_إضافة_نقطة_البيع_المحسنة.md", "تقرير_الألوان_الجذابة.md", "تقرير_الألوان_المتألقة_النهائي.md", "تقرير_الإصلاح_الشامل_النهائي.md", "تقرير_الإصلاح_النهائي.md", "تقرير_الإصلاحات_الشاملة.md", "تقرير_التبديل_الملون_النهائي.md", "تقرير_التحسينات_النهائي.md", "تقرير_التعديلات_النهائي.md", "تقرير_التقارير_المالية_المتقدمة.md", "تقرير_الفحص_الشامل_للبرنامج.md", "تقرير_القائمة_المنسدلة_الاحترافية_النهائي.md", "تقرير_القائمة_المنسدلة_النهائي.md", "تقرير_تبديل_الألوان_النهائي.md", "تقرير_تبديل_البيانات_النهائي.md", "تقرير_تثبيت_أيقونة_الموظفين.md", "تقرير_تحديث_الروابط_النهائي.md", "تقرير_تحسين_أزرار_الأصناف.md", "تقرير_تصميم_نافذة_الموظفين.md", "تقرير_حذف_العنصر_الفارغ_النهائي.md", "تقرير_حل_النص_العربي_matplotlib.md", "تقرير_حل_النص_العربي_المقلوب.md", "تقرير_دمج_القائمة_الرئيسية_المحسن.md", "تقرير_فحص_البرنامج_الشامل.md", "تقرير_فحص_النظام_الحالي.md", "تقرير_فحص_وتشغيل_البرنامج_2025.md", "تقرير_ملء_الشاشة_النهائي.md", "تقرير_نافذة_البيع_المحسنة.md", "تقرير_نافذة_الترحيب_النهائي.md", "تقرير_نظام_المبيعات_الشامل.md", "ملخص_الإصلاحات_النهائي.md", "ملخص_الفحص_النهائي.md", "نافذة_تحليل_المبيعات_الاحترافية.md", "نظام_إدارة_المخازن_المتكامل.md", "نظام_القيد_المزدوج.md", "نظام_المشتريات_المحسن.md", "نظام_المصادقة_والصلاحيات.md", "خوارزميات_شجرة_الحسابات.md", "دليل_الاستخدام_السريع.md", "دليل_التشغيل.md", "تم_إصلاح_نافذة_المشتريات.md", "تم_تطوير_النظام_المحاسبي.md", "تم_تغيير_بيانات_المدير.md", "change_admin_credentials.py", "comprehensive_dropdown_fix.py", "final_dropdown_test.py", "apply_fullscreen_to_all_windows.py", "install_advanced_libraries.py", "install_arabic_support.py", "install_barcode_requirements.py", "install_sqlserver_express.py", "install_sqlserver_requirements.py", "download_arabic_fonts.py", "setup_postgresql.py", "create_demo_users.py", "create_employees_icon.py", "save_ceo_image.py", "comprehensive_income_formula_demo.json", "comprehensive_income_test_summary.json", "sales_analysis_test_summary.json", "structured_profit_loss_test_summary.json", "warehouse_management_test_summary.json", "test_comprehensive_income.xlsx", "test_excel.xlsx", "test_report.pdf"], "deleted_directories": ["__pycache__", "backup_20250714_031100", "analysis", "examples", "docs", "integration", "tests"], "errors": [], "summary": {"total_files_deleted": 152, "total_dirs_deleted": 7, "total_errors": 0}}