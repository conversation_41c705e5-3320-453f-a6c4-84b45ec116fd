# ⚙️ نافذة الإعدادات المتقدمة - برنامج ست الكل للمحاسبة

## نظرة عامة

تم تطوير نافذة إعدادات متقدمة وشاملة تتيح للمستخدمين تخصيص وتكوين جميع جوانب النظام بطريقة سهلة ومنظمة. النافذة مصممة بواجهة عربية RTL حديثة مع تبويبات منظمة لسهولة الوصول إلى جميع الإعدادات.

## 🎯 الميزات الرئيسية

### 🎨 **التصميم والواجهة**
- ✅ **واجهة عربية RTL** كاملة مع خطوط أنيقة
- ✅ **تبويبات منظمة** مع شريط جانبي للتنقل السهل
- ✅ **ألوان حديثة ومتناسقة** مع التصميم العام
- ✅ **واجهة تفاعلية** مع معاينة فورية للتغييرات
- ✅ **تصميم متجاوب** يتكيف مع أحجام الشاشات المختلفة

### 📋 **أقسام الإعدادات**

#### 🔧 **1. الإعدادات العامة**
- **اللغة**: اختيار بين العربية والإنجليزية
- **حجم الخط**: تخصيص حجم الخط (10-20)
- **النسخ الاحتياطي التلقائي**: تفعيل/إلغاء تفعيل
- **فترة النسخ الاحتياطي**: تحديد الفترة بالساعات (1-168)
- **تفعيل الأصوات**: تشغيل/إيقاف الأصوات
- **تفعيل الإشعارات**: تشغيل/إيقاف الإشعارات

#### 🎨 **2. المظهر والثيم**
- **الثيم**: اختيار بين الفاتح، الداكن، أو التلقائي
- **اللون الأساسي**: اختيار لون مخصص للواجهة
- **لون الخلفية**: تخصيص لون الخلفية
- **الخط الافتراضي**: اختيار من خطوط عربية متعددة

#### 🏢 **3. معلومات الشركة**
- **اسم الشركة**: تحديد اسم الشركة
- **العنوان**: عنوان الشركة التفصيلي
- **رقم الهاتف**: أرقام الاتصال
- **البريد الإلكتروني**: عنوان البريد الرسمي
- **الرقم الضريبي**: رقم التسجيل الضريبي
- **شعار الشركة**: رفع وتحديد شعار الشركة

#### 💰 **4. الإعدادات المالية**
- **العملة**: اختيار العملة الأساسية
- **رمز العملة**: تحديد رمز العملة
- **معدل الضريبة**: تحديد نسبة الضريبة (0-50%)
- **عدد الأرقام العشرية**: دقة الحسابات (0-4)
- **السنة المالية**: تحديد بداية السنة المالية

#### 🔐 **5. الأمان والصلاحيات**
- **مهلة الجلسة**: تحديد مدة انتهاء الجلسة (5-480 دقيقة)
- **طول كلمة المرور**: الحد الأدنى لطول كلمة المرور (4-20)
- **محاولات تسجيل الدخول**: عدد المحاولات المسموحة (3-10)
- **تسجيل العمليات**: تفعيل تسجيل جميع العمليات

#### 💾 **6. قاعدة البيانات**
- **نوع قاعدة البيانات**: SQLite، PostgreSQL، MySQL
- **مسار قاعدة البيانات**: تحديد موقع ملف قاعدة البيانات
- **تحسين الأداء**: تفعيل تحسينات الأداء
- **ضغط البيانات**: تفعيل ضغط البيانات لتوفير المساحة

#### 📊 **7. التقارير والطباعة**
- **تنسيق التقرير الافتراضي**: PDF، Excel، Word
- **حجم الورق**: A4، Letter، A3
- **اتجاه الطباعة**: عمودي أو أفقي
- **تضمين الشعار**: إدراج شعار الشركة في التقارير

#### 🔄 **8. النسخ الاحتياطي**
- **مجلد النسخ الاحتياطي**: تحديد مكان حفظ النسخ
- **عدد النسخ المحفوظة**: عدد النسخ الاحتياطية المحتفظ بها (5-100)
- **ضغط النسخ الاحتياطية**: تقليل حجم ملفات النسخ
- **نسخ احتياطي عند الإغلاق**: حفظ تلقائي عند إغلاق البرنامج

#### 🌐 **9. الشبكة والاتصال**
- **تفعيل الشبكة**: تشغيل/إيقاف الاتصال بالشبكة
- **عنوان الخادم**: IP أو اسم الخادم
- **منفذ الاتصال**: رقم المنفذ (1000-65535)
- **مهلة الاتصال**: وقت انتظار الاتصال (5-60 ثانية)

## 🚀 كيفية الاستخدام

### 1. فتح نافذة الإعدادات
```python
# من النافذة الرئيسية
# اضغط على أيقونة "الإعدادات" في الصف الأول من الأيقونات
# أو من قائمة الإعدادات اختر "إعدادات النظام"

# أو برمجياً:
from ui.advanced_settings_window import AdvancedSettingsWindow
settings_window = AdvancedSettingsWindow()
```

### 2. التنقل بين الأقسام
- استخدم الشريط الجانبي للانتقال بين أقسام الإعدادات المختلفة
- كل قسم يحتوي على إعدادات متخصصة ومنظمة

### 3. تخصيص الإعدادات
- **الحقول النصية**: أدخل النص مباشرة
- **القوائم المنسدلة**: اختر من الخيارات المتاحة
- **مربعات الاختيار**: فعل/ألغ الخيارات
- **اختيار الألوان**: اضغط على "اختيار اللون" لفتح منتقي الألوان
- **اختيار الملفات**: اضغط على "تصفح" لاختيار الملفات

### 4. حفظ الإعدادات
- اضغط على "💾 حفظ الإعدادات" لحفظ جميع التغييرات
- سيتم حفظ الإعدادات في ملف `config/app_settings.json`
- بعض الإعدادات تتطلب إعادة تشغيل البرنامج

### 5. استعادة الإعدادات الافتراضية
- اضغط على "🔄 استعادة الافتراضي" لإعادة جميع الإعدادات إلى القيم الافتراضية
- سيتم حذف ملف الإعدادات المخصص

## 📁 هيكل الملفات

```
ui/
└── advanced_settings_window.py    # نافذة الإعدادات المتقدمة

config/
├── settings.py                    # الإعدادات الأساسية
├── app_settings.json             # الإعدادات المخصصة (يتم إنشاؤه تلقائياً)
└── scheduler_settings.py         # إعدادات الجدولة

test_settings.py                   # ملف اختبار النافذة
```

## 🔧 المتطلبات التقنية

### مكتبات Python المطلوبة
```bash
pip install customtkinter
pip install pillow              # لمعالجة الصور
```

### ملفات الإعدادات
- **`config/app_settings.json`**: يحتوي على جميع الإعدادات المخصصة
- **`config/settings.py`**: الإعدادات الأساسية والثوابت
- **`assets/fonts/`**: مجلد الخطوط العربية (اختياري)

## 🎯 الميزات المتقدمة

### نظام حفظ الإعدادات
```json
{
  "language": "العربية",
  "font_size": 12,
  "auto_backup": true,
  "backup_interval": 24,
  "theme": "فاتح",
  "primary_color": "#2E8B57",
  "company_name": "شركة ست الكل للمحاسبة",
  "currency": "ريال سعودي",
  "tax_rate": 15.0
}
```

### تطبيق الإعدادات
- **فوري**: الألوان، الخطوط، اللغة
- **عند إعادة التشغيل**: حجم النافذة، قاعدة البيانات، الثيم
- **عند الحاجة**: إعدادات الطباعة، النسخ الاحتياطي

### التحقق من صحة البيانات
- فحص صحة عناوين البريد الإلكتروني
- التأكد من صحة أرقام الهاتف
- فحص صحة مسارات الملفات
- التحقق من نطاقات القيم الرقمية

## 🔮 التطوير المستقبلي

### الميزات المخططة
- [ ] **استيراد/تصدير الإعدادات** من/إلى ملف
- [ ] **قوالب إعدادات جاهزة** للصناعات المختلفة
- [ ] **معاينة مباشرة** للتغييرات قبل الحفظ
- [ ] **إعدادات متقدمة للمطورين**
- [ ] **نظام إشعارات** للتغييرات المهمة

### التحسينات المخططة
- [ ] **بحث في الإعدادات** للوصول السريع
- [ ] **مساعد الإعداد الأولي** للمستخدمين الجدد
- [ ] **نسخ احتياطي للإعدادات** قبل التغيير
- [ ] **تصدير إعدادات محددة** فقط
- [ ] **مزامنة الإعدادات** عبر الأجهزة

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. لا تظهر النافذة
```python
# تأكد من استيراد المكتبات المطلوبة
import customtkinter as ctk
from ui.advanced_settings_window import AdvancedSettingsWindow
```

#### 2. خطأ في حفظ الإعدادات
```python
# تأكد من صلاحيات الكتابة في مجلد config/
# أو قم بتشغيل البرنامج كمدير
```

#### 3. لا تطبق الإعدادات
```python
# بعض الإعدادات تتطلب إعادة تشغيل البرنامج
# تأكد من حفظ الإعدادات قبل الإغلاق
```

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. تحقق من ملف السجلات في مجلد `logs/`
2. راجع ملف الإعدادات `config/app_settings.json`
3. استخدم "استعادة الافتراضي" في حالة المشاكل

---

**🎊 نافذة الإعدادات المتقدمة جاهزة للاستخدام مع جميع الميزات المطلوبة!**

**تم تطوير هذه النافذة بواسطة Augment Agent** 🤖  
**التاريخ: 19 يوليو 2024**
