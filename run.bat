@echo off
chcp 65001 > nul
title برنامج ست الكل للمحاسبة

echo ========================================
echo    برنامج ست الكل للمحاسبة
echo    نظام إدارة المبيعات والمحاسبة
echo ========================================
echo.

echo جاري التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo https://www.python.org/downloads/
    pause
    exit /b 1
)

echo جاري التحقق من المتطلبات...
if not exist "venv" (
    echo إنشاء البيئة الافتراضية...
    python -m venv venv
)

echo تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

echo تثبيت المتطلبات...
pip install -r requirements.txt > nul 2>&1

echo.
echo جاري تشغيل البرنامج...
echo.

python main.py

if errorlevel 1 (
    echo.
    echo حدث خطأ في تشغيل البرنامج
    echo يرجى التحقق من ملفات السجل في مجلد logs
    pause
)

echo.
echo تم إغلاق البرنامج
pause
