# 🚀 دليل تشغيل لوحة التحكم المركزية المطورة

## 📋 طرق التشغيل المختلفة

### 🎯 الطريقة الأولى: ملف Batch (الأسهل)
```
انقر نقراً مزدوجاً على: launch_advanced_control_panel.bat
```

### 🎯 الطريقة الثانية: سطر الأوامر
```bash
python test_advanced_control_panel.py
```

### 🎯 الطريقة الثالثة: Python مباشرة
```bash
py test_advanced_control_panel.py
```

### 🎯 الطريقة الرابعة: النقر المزدوج
```
انقر نقراً مزدوجاً على: test_advanced_control_panel.py
```

## 🔧 متطلبات التشغيل

### 1. Python
- **الإصدار المطلوب**: Python 3.8 أو أحدث
- **التحميل**: https://python.org
- **تأكد من إضافة Python إلى PATH أثناء التثبيت**

### 2. المكتبات المطلوبة
```bash
pip install customtkinter
```

## 🎨 ما ستراه عند التشغيل

### 🏠 الواجهة الرئيسية
- **العنوان**: لوحة التحكم المركزية المطورة
- **الحجم**: ملء الشاشة تلقائياً
- **اللغة**: عربية بالكامل مع دعم RTL

### 📂 الأقسام الـ11 المتاحة

1. **🧾 إعدادات الفواتير**
   - قوالب متعددة
   - إعدادات طباعة
   - ضرائب وخصومات
   - إعدادات POS

2. **💰 الرواتب والضرائب**
   - سلالم الرواتب
   - حاسبة الرواتب
   - شرائح الضرائب
   - إعدادات الإضافي

3. **🏪 إدارة المخازن**
   - إدارة المخازن
   - أذون الحركة
   - نظام الجرد
   - تكامل الباركود

4. **👥 المستخدمون والصلاحيات**
   - إدارة المستخدمين
   - الأدوار والصلاحيات
   - إعدادات الأمان

5. **🔧 التحكم في الموديلات**
   - تفعيل/إلغاء الميزات
   - موديلات أساسية ومتقدمة

6. **💾 النسخ الاحتياطي**
   - جدولة تلقائية
   - إدارة النسخ
   - استعادة البيانات

7. **📊 استيراد وتصدير**
   - استيراد من Excel
   - تصدير البيانات
   - التحقق من الصحة

8. **🎨 تخصيص الواجهة**
   - ثيمات جاهزة
   - منتقي الألوان
   - الخطوط العربية

9. **🛡️ نظام الأمان**
   - مستويات الأمان
   - التشفير
   - سجل العمليات

10. **🔢 الأرقام التسلسلية**
    - قوالب الترقيم
    - توليد تلقائي

11. **⚙️ إعدادات النظام**
    - معلومات الشركة
    - إعدادات عامة

## 🎉 رسالة الترحيب

عند التشغيل الناجح، ستظهر رسالة ترحيب تحتوي على:
- 🎉 ترحيب بالمستخدم
- ✨ قائمة بالميزات الجديدة
- 🚀 دعوة لاستكشاف الأقسام

## 🐛 حل المشاكل الشائعة

### ❌ Python غير موجود
**الحل:**
1. تحميل Python من https://python.org
2. تثبيت Python مع إضافته إلى PATH
3. إعادة تشغيل الكمبيوتر
4. المحاولة مرة أخرى

### ❌ customtkinter غير مثبت
**الحل:**
```bash
pip install customtkinter
```

### ❌ خطأ في الاستيراد
**الحل:**
1. تأكد من وجود جميع الملفات:
   - `ui/central_control_panel.py`
   - `ui/advanced_sections.py`
   - `ui/advanced_sections_part2.py`
   - `ui/control_panel_integration.py`
   - `themes/modern_theme.py`
   - `config/settings.py`

### ❌ مشاكل في الواجهة
**الحل:**
1. تأكد من دعم النظام للخطوط العربية
2. تحديث برامج تشغيل الشاشة
3. تجربة دقة شاشة مختلفة

## 📞 الدعم والمساعدة

### 🔍 فحص النظام
```bash
python test_advanced_control_panel.py --check
```

### 📋 تقرير الأخطاء
- سيتم عرض تفاصيل الأخطاء في النافذة
- يمكن نسخ رسائل الأخطاء للمساعدة

### 💬 طلب المساعدة
- تأكد من تضمين رسالة الخطأ الكاملة
- اذكر نظام التشغيل وإصدار Python
- وصف الخطوات التي أدت للمشكلة

## 🎯 نصائح للاستخدام الأمثل

### 🖥️ متطلبات الشاشة
- **الدقة المنصوح بها**: 1920x1080 أو أعلى
- **حجم الشاشة**: 15 بوصة أو أكبر للراحة

### ⚡ الأداء
- إغلاق البرامج غير الضرورية
- تأكد من توفر ذاكرة كافية (4GB RAM على الأقل)

### 🎨 التخصيص
- جرب الثيمات المختلفة
- اختر الألوان المناسبة لبيئة العمل
- اضبط أحجام الخطوط حسب الحاجة

## 🚀 الاستمتاع بالتجربة

لوحة التحكم المطورة تقدم:
- **واجهة عربية جميلة** مع ألوان دافئة
- **تحكم كامل** في جميع جوانب النظام
- **سهولة استخدام** مع تصميم بديهي
- **ميزات متقدمة** لإدارة احترافية

**استمتع بالتحكم الكامل في نظامك! 🎉**
