{"timestamp": "2025-07-22T08:41:37.022681", "project_structure": {"core_files_status": {"main.py": {"exists": true, "size": 3223, "readable": true, "syntax_valid": true}, "ui/main_window.py": {"exists": true, "size": 101377, "readable": true, "syntax_valid": true}, "ui/login_window.py": {"exists": true, "size": 10239, "readable": true, "syntax_valid": true}, "database/hybrid_database_manager.py": {"exists": true, "size": 18746, "readable": true, "syntax_valid": true}, "auth/auth_manager.py": {"exists": true, "size": 11303, "readable": true, "syntax_valid": true}}, "ui_modules_status": {"ui/pos_window.py": {"exists": true, "size": 33865, "readable": true, "syntax_valid": true, "importable": false}, "ui/pos_simple.py": {"exists": true, "size": 16043, "readable": true, "syntax_valid": true, "importable": false}, "ui/advanced_settings_window.py": {"exists": true, "size": 126348, "readable": true, "syntax_valid": true, "importable": false}, "ui/sales_analysis_window.py": {"exists": true, "size": 56086, "readable": true, "syntax_valid": false, "importable": false}, "ui/accounts_window.py": {"exists": true, "size": 18238, "readable": true, "syntax_valid": true, "importable": false}, "ui/reports_window.py": {"exists": true, "size": 22432, "readable": true, "syntax_valid": true, "importable": false}}, "database_modules_status": {"database/database_manager.py": {"exists": true, "size": 36334, "readable": true, "syntax_valid": true, "importable": false}, "database/products_manager.py": {"exists": true, "size": 17326, "readable": true, "syntax_valid": true, "importable": false}, "database/invoices_manager.py": {"exists": true, "size": 19464, "readable": true, "syntax_valid": true, "importable": false}, "database/accounts_manager.py": {"exists": true, "size": 13694, "readable": true, "syntax_valid": true, "importable": false}}, "service_modules_status": {"services/sales_manager.py": {"exists": true, "size": 21437, "readable": true, "syntax_valid": true, "importable": false}, "services/purchases_manager.py": {"exists": true, "size": 16003, "readable": true, "syntax_valid": true, "importable": false}, "services/treasury_manager.py": {"exists": true, "size": 13954, "readable": true, "syntax_valid": true, "importable": false}}, "config_files_status": {"config/settings.py": {"exists": true, "size": 2410, "readable": true, "syntax_valid": true, "importable": false}, "themes/theme_manager.py": {"exists": true, "size": 10442, "readable": true, "syntax_valid": true, "importable": false}, "core/scheduler_manager.py": {"exists": true, "size": 12661, "readable": true, "syntax_valid": true, "importable": false}}, "missing_files": [], "extra_files": [], "directory_structure": {"ui": {"exists": true, "is_directory": true, "file_count": 39}, "database": {"exists": true, "is_directory": true, "file_count": 16}, "services": {"exists": true, "is_directory": true, "file_count": 7}, "config": {"exists": true, "is_directory": true, "file_count": 6}, "themes": {"exists": true, "is_directory": true, "file_count": 4}, "core": {"exists": true, "is_directory": true, "file_count": 5}, "auth": {"exists": true, "is_directory": true, "file_count": 2}, "assets": {"exists": true, "is_directory": true, "file_count": 0}}}, "code_analysis": {"syntax_errors": [{"file": "advanced_error_analyzer.py", "line": 103, "message": "unterminated string literal (detected at line 103)", "text": "module_name = str(file_path).replace('/', '.').replace('\\', '.').replace('.py', '')"}, {"file": "advanced_error_fixer.py", "line": 147, "message": "unterminated string literal (detected at line 147)", "text": "(r'\\\\([^\\\\nrtbfav'\"0-7xuUN])', r'\\\\\\\\1'),"}, {"file": "advanced_syntax_fixer.py", "line": 159, "message": "unterminated string literal (detected at line 159)", "text": "content = re.sub(r'\\\\(?![nrtbfav\\\\'\"0-7xuUN])', r'\\\\\\', content)"}, {"file": "comprehensive_income_formula_demo.py", "line": 254, "message": "unexpected indent", "text": "traceback.print_exc()"}, {"file": "deep_comprehensive_fixer.py", "line": 170, "message": "unexpected character after line continuation character", "text": "r'\\1if hasattr(self, '\\2') and self.\\2:\\n\\1    self.\\2.destroy()'),"}, {"file": "deep_import_fixer.py", "line": 202, "message": "unterminated string literal (detected at line 202)", "text": "module_name = str(file_path).replace('/', '.').replace('\\', '.').replace('.py', '')"}, {"file": "quick_pattern_fixer.py", "line": 35, "message": "invalid syntax", "text": "replacement1 = r'\\1if hasattr(self, 'window') and self.window:\\n\\1    self.window.destroy()'"}, {"file": "run_app.py", "line": 43, "message": "unexpected indent", "text": "app = MainApplication()"}, {"file": "run_fixed_app.py", "line": 155, "message": "unexpected indent", "text": "logger.info(\"✅ تم استيراد التطبيق الرئيسي\")"}, {"file": "safe_start.py", "line": 42, "message": "unexpected indent", "text": "app = MainApplication()"}, {"file": "start_with_scheduler.py", "line": 81, "message": "unexpected indent", "text": "app = MainApplication()"}, {"file": "ultimate_system_fixer.py", "line": 302, "message": "unterminated string literal (detected at line 302)", "text": "line = re.sub(r'\\\\([^\\\\nrtbfav'\"0-7xuUN])', r'\\\\\\\\1', line)"}, {"file": "config\\postgresql_config.py", "line": 151, "message": "unexpected indent", "text": "conn = psycopg2.connect(**config)"}, {"file": "core\\app_core.py", "line": 299, "message": "unexpected indent", "text": "db = DatabaseManager()"}, {"file": "database\\comprehensive_income_manager.py", "line": 274, "message": "invalid syntax", "text": "def _calculate_financial_ratios(self, revenues: float, gross_profit: float:"}, {"file": "database\\fix_database.py", "line": 69, "message": "unterminated string literal (detected at line 69)", "text": "(7, 'ملابس رجالية', 'Men's Clothing', 6),"}, {"file": "ui\\daily_journal_window.py", "line": 686, "message": "invalid syntax", "text": "finally:"}, {"file": "ui\\sales_analysis_window.py", "line": 1340, "message": "unexpected indent", "text": "filename = filedialog.asksaveasfilename("}], "import_errors": [], "undefined_variables": [], "unused_imports": [], "code_quality_issues": [], "library_compatibility": {"customtkinter": {"available": true, "version": "5.2.2", "meets_requirement": true, "error": null}, "tkinter": {"available": true, "version": "8.6", "meets_requirement": true, "error": null}, "sqlite3": {"available": true, "version": "unknown", "meets_requirement": true, "error": null}, "PIL": {"available": true, "version": "11.3.0", "meets_requirement": true, "error": null}, "apscheduler": {"available": true, "version": "3.11.0", "meets_requirement": true, "error": null}, "pathlib": {"available": true, "version": "unknown", "meets_requirement": true, "error": null}, "datetime": {"available": true, "version": "unknown", "meets_requirement": true, "error": null}, "json": {"available": true, "version": "2.0.9", "meets_requirement": true, "error": null}, "logging": {"available": true, "version": "0.5.1.2", "meets_requirement": true, "error": null}}, "total_files_analyzed": 122, "healthy_files": 104, "problematic_files": 18}, "database_analysis": {"sqlite_available": true, "database_file_exists": false, "database_accessible": false, "tables_analysis": {}, "data_integrity": {}, "performance_metrics": {}, "backup_system": {"backup_dir_exists": true, "backup_count": 2, "latest_backup": "scheduled_backup_20250712_191621.db"}}, "functional_tests": {"ui_components": {"ui/main_window.py": {"exists": true, "importable": true, "has_main_class": true, "error": null}, "ui/login_window.py": {"exists": true, "importable": true, "has_main_class": true, "error": null}, "ui/pos_window.py": {"exists": true, "importable": true, "has_main_class": true, "error": null}, "ui/advanced_settings_window.py": {"exists": true, "importable": true, "has_main_class": true, "error": null}}, "core_functions": {"database/hybrid_database_manager.py": {"exists": true, "importable": true, "functional": true, "error": null}, "services/sales_manager.py": {"exists": true, "importable": true, "functional": true, "error": null}, "auth/auth_manager.py": {"exists": true, "importable": true, "functional": true, "error": null}, "core/scheduler_manager.py": {"exists": true, "importable": true, "functional": true, "error": null}}, "integration_tests": {}, "user_workflows": {}}, "performance_metrics": {"file_sizes": {"advanced_error_analyzer.py": 16230, "advanced_error_fixer.py": 12505, "advanced_syntax_fixer.py": 10948, "cleanup_unnecessary_files.py": 8562, "comma_fixer.py": 1739, "comprehensive_import_fixer.py": 13216, "comprehensive_income_formula_demo.py": 14320, "comprehensive_syntax_fixer.py": 14192, "comprehensive_systematic_audit.py": 34104, "comprehensive_system_checker.py": 16466, "critical_file_fixer.py": 10877, "database_analyzer.py": 11921, "deep_comprehensive_fixer.py": 18348, "deep_import_fixer.py": 12181, "duplicate_import_cleaner.py": 3622, "escape_character_fixer.py": 1542, "example_code.py": 4092, "except_block_fixer.py": 6321, "final_cleanup_tool.py": 10517, "fix_remaining_imports.py": 2729, "local_sqlserver_config.py": 699, "main.py": 3223, "performance_optimizer.py": 10767, "precise_syntax_fixer.py": 9718, "quick_pattern_fixer.py": 3730, "run_app.py": 2214, "run_fixed_app.py": 8874, "safe_main.py": 5985, "safe_start.py": 1454, "setup.py": 2214, "start_app.py": 1692, "start_with_scheduler.py": 4319, "syntax_error_fixer.py": 10246, "test_hr_window.py": 2175, "test_settings.py": 3380, "ultimate_advanced_fixer.py": 15608, "ultimate_comprehensive_diagnostic.py": 15457, "ultimate_system_fixer.py": 18114, "auth\\auth_manager.py": 11303, "auth\\__init__.py": 91, "config\\arabic_fonts.py": 2903, "config\\postgresql_config.py": 7703, "config\\scheduler_settings.py": 4953, "config\\settings.py": 2410, "config\\sqlserver_config.py": 7818, "config\\__init__.py": 25, "core\\app_core.py": 13983, "core\\barcode_scanner.py": 11442, "core\\error_handler.py": 9311, "core\\scheduler_manager.py": 12661, "core\\__init__.py": 87, "database\\accounts_manager.py": 13694, "database\\accounts_tree_manager.py": 25645, "database\\comprehensive_income_manager.py": 34244, "database\\database_manager.py": 36334, "database\\fix_database.py": 18273, "database\\hybrid_database_manager.py": 18746, "database\\invoices_database_manager.py": 22328, "database\\invoices_manager.py": 19464, "database\\journal_entries_manager.py": 13427, "database\\postgresql_manager.py": 19375, "database\\products_manager.py": 17326, "database\\profit_loss_structure_manager.py": 47478, "database\\reports_manager.py": 12432, "database\\sqlserver_manager.py": 17241, "database\\warehouse_manager.py": 29295, "database\\__init__.py": 89, "models\\customer.py": 8442, "models\\invoice.py": 12701, "models\\product.py": 11884, "models\\__init__.py": 84, "reports\\report_generator.py": 18823, "reports\\__init__.py": 86, "services\\employees_manager.py": 11995, "services\\invoice_printer.py": 19282, "services\\postgresql_sales_manager.py": 20260, "services\\purchases_manager.py": 16003, "services\\sales_manager.py": 21437, "services\\treasury_manager.py": 13954, "services\\__init__.py": 65, "themes\\font_manager.py": 11804, "themes\\modern_theme.py": 4605, "themes\\theme_manager.py": 10442, "themes\\__init__.py": 78, "ui\\accounts_tree_window.py": 20744, "ui\\accounts_window.py": 18238, "ui\\add_items_window.py": 76949, "ui\\advanced_financial_reports_window.py": 53564, "ui\\advanced_settings_window.py": 126348, "ui\\categories_management_window.py": 29202, "ui\\comprehensive_income_window.py": 27275, "ui\\comprehensive_sales_window.py": 20720, "ui\\daily_journal_window.py": 39490, "ui\\employees_window.py": 40006, "ui\\employees_window_fixed.py": 24889, "ui\\enhanced_pos_window.py": 39594, "ui\\hr_management_window.py": 90781, "ui\\inventory_window.py": 28874, "ui\\invoices_main_window.py": 15109, "ui\\invoices_reports_window.py": 17303, "ui\\journal_entries_window.py": 20033, "ui\\login_window.py": 10239, "ui\\main_window.py": 101377, "ui\\pos_simple.py": 16043, "ui\\pos_system_window.py": 18751, "ui\\pos_window.py": 33865, "ui\\purchases_window.py": 21322, "ui\\reports_window.py": 22432, "ui\\sales_analysis_window.py": 56086, "ui\\sales_invoice_window.py": 18041, "ui\\sales_manager.py": 13172, "ui\\sales_window.py": 21743, "ui\\simple_welcome_window.py": 12532, "ui\\stock_management_window.py": 64725, "ui\\structured_profit_loss_window.py": 30627, "ui\\treasury_window.py": 26404, "ui\\units_management_window.py": 22469, "ui\\user_management.py": 20021, "ui\\warehouses_management_window.py": 97278, "ui\\welcome_window.py": 15422, "ui\\window_utils.py": 5921, "ui\\__init__.py": 95}, "import_times": {}, "memory_usage": {}, "startup_time": null, "recommendations": ["مراجعة حجم الملف: ui\\advanced_settings_window.py", "مراجعة حجم الملف: ui\\main_window.py", "مراجعة حجم الملف: ui\\warehouses_management_window.py", "مراجعة حجم الملف: ui\\hr_management_window.py", "مراجعة حجم الملف: ui\\add_items_window.py"]}, "recommendations": [], "overall_score": 72.6, "score_breakdown": {"structure": 95.23809523809523, "code": 53.67213114754098, "database": 50, "functional": 100.0}}